"""
Basitleştirilmiş Doküman Parser <PERSON>
- LlamaIndex: Tüm dokümanlar için ana parser
- OCR: Sad<PERSON><PERSON> gö<PERSON><PERSON>ler için
- Legacy parsers: Özel formatlar için fallback
"""

import os
import tempfile
import magic
from typing import List, Dict, Any
import logging
from PIL import Image
import warnings

# NNPACK uyarılarını bastır (CPU'da hızlandırma için kullan<PERSON>lı<PERSON>, olmaması kritik değil)
warnings.filterwarnings("ignore", category=UserWarning, message=".*Could not initialize NNPACK.*")


# LlamaIndex (zorunlu - tüm formatlar için)
from llama_index.core import SimpleDirectoryReader
# ☁️ LlamaParse'ı import et
from llama_index.core.node_parser import SimpleNodeParser
from llama_parse import LlamaParse

# OCR (zorunlu - sadece görüntüler için)
import easyocr

# PDF OCR için PyMuPDF
import fitz
import re

# Legacy formatlar için gerekli importlar
import openpyxl
import json
import csv
import xml.etree.ElementTree as ET
from markdownify import markdownify as md

# Logging konfigürasyonu
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AdvancedDocumentParser:
    """
    Akıllı ve hibrit doküman parser'ı
    Strateji:
    1. Yalnızca Görüntü mü? -> Hızlı OCR kullan (easyocr)
    2. Karmaşık Belge mi (PDF, DOCX vb.)? 
       - Önce LlamaParse'ı dene (Bulut)
       - Başarısız olursa -> Lokal Hibrit Parser'a düş (Lokal)
       - O da başarısız olursa -> Basit Parser'a düş (Lokal)
    """
    
    def __init__(self):
        # 1. LlamaParse API anahtarlarını yükle
        logger.info("🤖 LlamaParse API anahtarları kontrol ediliyor...")
        self.llama_cloud_api_keys = []
        try:
            api_keys_str = os.getenv("LLAMA_CLOUD_API_KEY", "")
            if api_keys_str:
                self.llama_cloud_api_keys = [key.strip() for key in api_keys_str.split(',') if key.strip()]
                if self.llama_cloud_api_keys:
                    logger.info(f"🔑 {len(self.llama_cloud_api_keys)} adet LlamaParse API anahtarı bulundu. Kullanım sırasında denenecekler.")
                else:
                    logger.warning("⚠️ LLAMA_CLOUD_API_KEY ortam değişkeni tanımlı ancak boş. Sadece lokal parser'lar kullanılacak.")
            else:
                logger.warning("⚠️ LlamaParse için LLAMA_CLOUD_API_KEY ortam değişkeni bulunamadı. Sadece lokal parser'lar kullanılacak.")
        except Exception as e:
            logger.error(f"❌ LlamaParse API anahtarları okunurken hata: {e}. Sadece lokal parser'lar kullanılacak.")

        # 2. Lokal OCR'ı başlat
        logger.info("👁️ EasyOCR (lokal hibrit parser için) yükleniyor...")
        try:
            self.ocr_reader = easyocr.Reader(['tr'], gpu=False, verbose=False)
            logger.info("✅ EasyOCR başarıyla yüklendi.")
        except Exception as e:
            logger.error(f"❌ EasyOCR yüklenemedi: {e}")
            self.ocr_reader = None

        # 3. Desteklenen formatlar
        self.supported_formats = {
            '.pdf', '.docx', '.pptx', '.xlsx', '.doc', '.xls', '.ppt',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp',
            '.txt', '.md', '.rtf', '.html', '.htm', '.xml', '.csv',
            '.json', '.tsv', '.epub', '.odt', '.ods', '.odp'
        }
        logger.info(f"📋 Toplam {len(self.supported_formats)} format destekleniyor.")

    def _normalize_filename(self, filename: str) -> str:
        """Dosya adını güvenli bir formata dönüştür"""
        
        filename = filename.replace('İ', 'I').replace('ı', 'i')
        filename = filename.replace('Ö', 'O').replace('ö', 'o')
        filename = filename.replace('Ü', 'U').replace('ü', 'u')
        filename = filename.replace('Ş', 'S').replace('ş', 's')
        filename = filename.replace('Ç', 'C').replace('ç', 'c')
        filename = filename.replace('Ğ', 'G').replace('ğ', 'g')
        filename = filename.replace(' ', '_')
        
        return re.sub(r'[^a-zA-Z0-9_.-]', '', filename)

    def detect_file_type(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Dosya türünü ve özelliklerini tespit et"""
        try:
            ext = os.path.splitext(filename.lower())[1]
            mime_type = magic.from_buffer(file_content, mime=True)
            is_image = mime_type.startswith('image/') or ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']
            
            return {
                'extension': ext,
                'mime_type': mime_type,
                'is_image': is_image,
                'encoding': 'utf-8'
            }
        except Exception as e:
            logger.warning(f"Dosya türü tespit hatası: {e}")
            ext = os.path.splitext(filename.lower())[1]
            return {
                'extension': ext,
                'mime_type': 'application/octet-stream',
                'is_image': ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'],
                'encoding': 'utf-8'
            }

    # --- Lokal Hibrit Parser Fonksiyonları (Fallback için) ---

    def parse_pptx_manual(self, file_path: str) -> List[str]:
        """Python-pptx ile manuel PPTX parsing (Metin çıkar)"""
        try:
            from pptx import Presentation
            
            prs = Presentation(file_path)
            texts = []
            
            for i, slide in enumerate(prs.slides):
                slide_texts = []
                for shape in slide.shapes:
                    if not shape.has_text_frame:
                        continue
                    for paragraph in shape.text_frame.paragraphs:
                        for run in paragraph.runs:
                            slide_texts.append(run.text)
                
                if slide_texts:
                    full_slide_text = " ".join(slide_texts).strip()
                    if full_slide_text:
                        texts.append(f"--- Slayt {i+1} ---\n{full_slide_text}")
            
            logger.info(f"✅ Lokal PPTX parsing başarılı: {len(texts)} slayttan metin çıkarıldı.")
            return texts
        except Exception as e:
            logger.error(f"❌ Lokal PPTX parsing hatası: {e}")
            return []

    def parse_docx_manual(self, file_path: str) -> List[str]:
        """Python-docx ile manuel DOCX parsing (Hibrit: Metin + OCR)"""
        try:
            from docx import Document
            
            doc = Document(file_path)
            texts = []
            full_text_content = []

            for para in doc.paragraphs:
                if para.text.strip():
                    full_text_content.append(para.text.strip())
            
            for i, table in enumerate(doc.tables):
                table_text = f"\n--- Tablo {i+1} ---\n"
                for row in table.rows:
                    row_texts = [cell.text.strip() for cell in row.cells]
                    table_text += " | ".join(row_texts) + "\n"
                full_text_content.append(table_text)
            
            if full_text_content:
                texts.append("\n".join(full_text_content))

            image_count = 0
            for rel in doc.part.rels.values():
                if "image" in rel.target_ref:
                    image_count += 1
                    image_bytes = rel.target_part.blob
                    with tempfile.NamedTemporaryFile(delete=True, suffix=".png") as temp_img:
                        temp_img.write(image_bytes)
                        temp_img.flush()
                        ocr_results = self.parse_image_with_ocr(temp_img.name)
                        if ocr_results:
                            ocr_text = " ".join(ocr_results)
                            texts.append(f"\n🖼️ [GÖRSEL {image_count} OCR]: {ocr_text}")
            
            logger.info(f"✅ Lokal hibrit DOCX parsing başarılı: {len(texts)} bölüm.")
            return texts
            
        except Exception as e:
            logger.error(f"❌ Lokal hibrit DOCX parsing hatası: {e}")
            return []

    def parse_pdf_hybrid(self, file_path: str) -> List[str]:
        """PDF'leri hibrit olarak parse et: metinleri çıkar, görselleri OCR'la"""
        texts = []
        try:
            doc = fitz.open(file_path)
            for i, page in enumerate(doc):
                page_content = []
                
                text_from_page = page.get_text("text", sort=True)
                if text_from_page and text_from_page.strip():
                    page_content.append(text_from_page.strip())

                image_list = page.get_images(full=True)
                if image_list:
                    for img_index, img in enumerate(image_list):
                        xref = img[0]
                        base_image = doc.extract_image(xref)
                        image_bytes = base_image["image"]
                        
                        with tempfile.NamedTemporaryFile(suffix=".png", delete=True) as temp_img:
                            temp_img.write(image_bytes)
                            temp_img.flush()
                            ocr_result = self.parse_image_with_ocr(temp_img.name)
                            if ocr_result:
                                page_content.append(f"🖼️ [GÖRSEL {img_index+1} OCR]: {' '.join(ocr_result)}")
                
                if page_content:
                    texts.append(f"=== Sayfa {i+1} ===\n\n".join(page_content))

            doc.close()
            logger.info(f"✅ Lokal hibrit PDF parsing başarılı: {len(texts)} sayfa işlendi.")
            return texts
        except Exception as e:
            logger.error(f"❌ Lokal hibrit PDF parsing hatası: {e}")
            return []

    def parse_image_with_ocr(self, file_path: str) -> List[str]:
        """Görüntülerden OCR ile metin çıkar (Bellek dostu ve daha güvenli)"""
        if not self.ocr_reader:
            logger.error("OCR reader mevcut değil, görüntü işlenemiyor.")
            return []
        
        # Decompression bomb koruması
        Image.MAX_IMAGE_PIXELS = 128_000_000  # ~128 Megapiksel'den büyük resim açmayı reddet

        try:
            logger.info(f"🖼️ Görüntü OCR ile işleniyor: {os.path.basename(file_path)}")
            
            # Bellek taşmasını önlemek için büyük görüntüleri yeniden boyutlandır
            MAX_IMAGE_SIZE = 1600  # Eşiği daha güvenli bir seviyeye düşürdük
            
            logger.info("--> Adım 1: Görüntü dosyası açılıyor...")
            with Image.open(file_path) as img:
                logger.info("--> Adım 2: Görüntü başarıyla açıldı. Mod kontrolü ve dönüştürme...")
                # Görüntüyü RGB'ye dönüştür (bazı formatlar için gerekli)
                if img.mode not in ("RGB", "L"):
                    img = img.convert("RGB")

                width, height = img.size
                logger.info(f"--> Orijinal boyut: {width}x{height}")
                
                if width > MAX_IMAGE_SIZE or height > MAX_IMAGE_SIZE:
                    logger.info(f"--> Görüntü çok büyük, yeniden boyutlandırılıyor...")
                    img.thumbnail((MAX_IMAGE_SIZE, MAX_IMAGE_SIZE), Image.Resampling.LANCZOS)
                    logger.info(f"--> Yeni boyut: {img.width}x{img.height}")

                logger.info("--> Adım 3: Görüntü numpy dizisine dönüştürülüyor...")
                import numpy as np
                img_np = np.array(img)
            
            logger.info("--> Adım 4: Görüntü EasyOCR motoruna gönderiliyor...")
            result = self.ocr_reader.readtext(img_np, detail=0, paragraph=True)
            logger.info("--> Adım 5: EasyOCR işlemi tamamlandı.")
            
            if not result:
                logger.warning("OCR sonucu boş.")
                return []
            
            logger.info("✅ Görüntü OCR başarılı.")
            return result
                
        except Image.DecompressionBombError as dbe:
            logger.error(f"❌ Decompression Bomb hatası! Görüntü işlenemiyor: {os.path.basename(file_path)}. Hata: {dbe}")
            return []
        except Exception as e:
            if "cannot identify image file" in str(e):
                 logger.error(f"❌ Görüntü dosyası bozuk veya tanınamıyor: {os.path.basename(file_path)}")
            else:
                logger.error(f"OCR hatası: {e}")
            return []

    def parse_document(self, file_content: bytes, filename: str) -> List[str]:
        """
        Ana hibrit parsing fonksiyonu (kademeli fallback ile)
        """
        safe_filename = self._normalize_filename(filename)
        file_info = self.detect_file_type(file_content, safe_filename)
        extension = file_info['extension']
        
        logger.info(f"📄 Parsing: {safe_filename} ({extension})")

        if extension not in self.supported_formats:
            logger.warning(f"❌ Desteklenmeyen format: {extension}")
            return []

        with tempfile.NamedTemporaryFile(suffix=extension, delete=False, mode='wb') as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        texts = []
        try:
            # Strateji 1: Dosya yalnızca bir görüntü ise hızlı lokal OCR kullan
            if file_info['is_image']:
                texts = self.parse_image_with_ocr(temp_file_path)
                return texts
                
            # Strateji 2: LlamaParse'ı sırayla dene (eğer API anahtarları varsa)
            if self.llama_cloud_api_keys:
                for i, api_key in enumerate(self.llama_cloud_api_keys):
                    try:
                        logger.info(f"🤖 LlamaParse deneniyor ({i+1}/{len(self.llama_cloud_api_keys)}. anahtar): {safe_filename}")
                        
                        parser = LlamaParse(
                            api_key=api_key,
                            result_type="markdown",
                            verbose=True,
                            language="tr"
                        )
                        documents = parser.load_data(temp_file_path)
                        
                        if documents and any(doc.get_content().strip() for doc in documents):
                            logger.info(f"✅ LlamaParse başarılı ({i+1}. anahtar kullanıldı).")
                            texts = [doc.get_content() for doc in documents if doc.get_content().strip()]
                            break  # Başarılı, döngüden çık
                        else:
                            logger.warning(f"LlamaParse ({i+1}. anahtar) '{safe_filename}' için içerik döndürmedi. Sonraki anahtar denenecek.")
                    
                    except Exception as e:
                        logger.warning(f"❌ LlamaParse hatası ({i+1}. anahtar): {e}. Sonraki anahtar denenecek.")
                        continue # Sonraki anahtarı dene
            
            if self.llama_cloud_api_keys and not texts:
                logger.warning("⚠️ Tüm LlamaParse anahtarları denendi ancak sonuç alınamadı. Lokal parser'a fallback yapılıyor.")
            
            # Strateji 3: Lokal Hibrit Parser (LlamaParse başarısız olduysa veya yoksa)
            if not texts:
                logger.info(f"🔄 Lokal hibrit parser deneniyor: {safe_filename}")
                if extension == '.pdf':
                    texts = self.parse_pdf_hybrid(temp_file_path)
                elif extension == '.docx':
                    texts = self.parse_docx_manual(temp_file_path)
                elif extension == '.pptx':
                    texts = self.parse_pptx_manual(temp_file_path)
                # Not: Diğer formatlar için de manuel parser'lar eklenebilir.
                # Şimdilik SimpleDirectoryReader'a düşecekler.

            # Strateji 4: Son Çare - Basit Lokal Parser
            if not texts:
                logger.info(f"🔄 Son çare olarak basit lokal parser (SimpleDirectoryReader) deneniyor...")
                try:
                    reader = SimpleDirectoryReader(input_files=[temp_file_path], recursive=False)
                    documents = reader.load_data()
                    texts = [doc.text for doc in documents if doc.text and doc.text.strip()]
                    if texts:
                        logger.info("✅ Basit lokal parser başarılı.")
                    else:
                        logger.warning("Basit lokal parser da içerik bulamadı.")
                except Exception as e:
                    logger.error(f"❌ Basit lokal parser da başarısız oldu: {e}")

            return texts

        finally:
            try:
                os.unlink(temp_file_path)
            except OSError as e:
                logger.warning(f"Geçici dosya silinemedi: {e}")
                
    def get_supported_formats(self) -> List[str]:
        """Desteklenen format listesini döndür"""
        return sorted(list(self.supported_formats))

    def is_supported_format(self, filename: str) -> bool:
        """Dosya formatının desteklenip desteklenmediğini kontrol et"""
        extension = os.path.splitext(filename.lower())[1]
        return extension in self.supported_formats

# Global parser instance
document_parser = AdvancedDocumentParser()

# Backward compatibility fonksiyonları
def extract_text_from_document(file_content: bytes, filename: str) -> str:
    """Eski API uyumluluğu için"""
    texts = document_parser.parse_document(file_content, filename)
    return "\n\n".join(texts) if texts else ""

def get_supported_file_extensions() -> List[str]:
    """Desteklenen dosya uzantılarını getir"""
    return document_parser.get_supported_formats()

def is_supported_file(filename: str) -> bool:
    """Dosyanın desteklenip desteklenmediğini kontrol et"""
    return document_parser.is_supported_format(filename) 
