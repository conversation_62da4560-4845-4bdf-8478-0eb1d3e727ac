import os
import json
import re
from typing import Dict, Any, List, Optional
import requests
from bs4 import BeautifulSoup
from youtube_transcript_api import YouTubeTran<PERSON><PERSON><PERSON><PERSON>
from pytube import YouTube
import docx
import PyPDF2
from io import BytesIO
from llama_index.core.text_splitter import SentenceSplitter

from document_parser import extract_text_from_document, is_supported_file
from qdrant_client import QdrantClient
from qdrant_client.models import VectorParams, Distance, PointStruct
import uuid
from dotenv import load_dotenv
from datetime import datetime, timezone
import numpy as np

load_dotenv()

OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
OPENROUTER_URL = os.getenv("OPENROUTER_URL")
OPENROUTER_MODEL = os.getenv("OPENROUTER_MODEL")
QDRANT_URL = os.getenv("QDRANT_URL")
OLLAMA_URL = os.getenv("OLLAMA_URL")
OLLAMA_EMBEDDER_MODEL = os.getenv("OLLAMA_EMBEDDER_MODEL")
EMBEDDER_DIMENSIONS = int(os.getenv("EMBEDDER_DIMENSIONS"))


qdrant_client = QdrantClient(url=QDRANT_URL)

def extract_video_id(youtube_url: str) -> str:
    """Extract the video ID from a YouTube URL."""
    try:
        if "youtube.com/watch" in youtube_url:
            if "v=" in youtube_url:
                video_id = youtube_url.split("v=")[1]
                if "&" in video_id:
                    video_id = video_id.split("&")[0]
                return video_id
            else:
                raise ValueError(f"Could not find video ID in YouTube URL: {youtube_url}")
        elif "youtu.be/" in youtube_url:
            video_id = youtube_url.split("youtu.be/")[1]
            if "?" in video_id:
                video_id = video_id.split("?")[0]
            return video_id
        else:
            raise ValueError(f"Invalid YouTube URL format: {youtube_url}")
    except Exception as e:
        raise ValueError(f"Could not extract video ID from URL: {youtube_url}")

def get_transcript_from_youtube_api(video_id: str) -> str:
    """Get transcript using YouTube Transcript API with Turkish language."""
    try:
        transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=['tr'])

        full_text = ""
        for entry in transcript:
            text = entry.get("text", "")
            text = re.sub(r'\[.*?\]', '', text)
            text = re.sub(r'\(.*?\)', '', text)
            full_text += text + " "

        full_text = re.sub(r'\s+', ' ', full_text).strip()
        full_text = clean_text(full_text)

        return full_text
    except Exception:
        return ""

def get_transcript_from_available_languages(video_id: str) -> str:
    """Get transcript from any available language."""
    try:
        transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)

        try:
            for transcript in transcript_list._manually_created_transcripts.values():
                try:
                    transcript_data = transcript.fetch()
                    full_text = ""
                    for entry in transcript_data:
                        text = entry.get("text", "")
                        text = re.sub(r'\[.*?\]', '', text)
                        text = re.sub(r'\(.*?\)', '', text)
                        full_text += text + " "

                    full_text = re.sub(r'\s+', ' ', full_text).strip()
                    full_text = clean_text(full_text)

                    if full_text:
                        return full_text
                except Exception:
                    continue
        except Exception:
            pass

        try:
            for transcript in transcript_list._generated_transcripts.values():
                try:
                    transcript_data = transcript.fetch()
                    full_text = ""
                    for entry in transcript_data:
                        text = entry.get("text", "")
                        text = re.sub(r'\[.*?\]', '', text)
                        text = re.sub(r'\(.*?\)', '', text)
                        full_text += text + " "

                    full_text = re.sub(r'\s+', ' ', full_text).strip()
                    full_text = clean_text(full_text)

                    if full_text:
                        return full_text
                except Exception:
                    continue
        except Exception:
            pass

        try:
            all_transcripts = list(transcript_list._manually_created_transcripts.values()) + list(transcript_list._generated_transcripts.values())

            for transcript in all_transcripts:
                try:
                    transcript_data = transcript.fetch()
                    full_text = ""
                    for entry in transcript_data:
                        text = entry.get("text", "")
                        text = re.sub(r'\[.*?\]', '', text)
                        text = re.sub(r'\(.*?\)', '', text)
                        full_text += text + " "

                    full_text = re.sub(r'\s+', ' ', full_text).strip()
                    full_text = clean_text(full_text)

                    if full_text:
                        return full_text
                except Exception:
                    continue
        except Exception:
            pass

        return ""
    except Exception:
        return ""

def get_transcript_from_pytube(youtube_url: str) -> str:
    """Get transcript using PyTube captions (best effort)."""
    try:
        yt = YouTube(youtube_url)
        if not yt.captions:
            return ""

        caption = yt.captions.get_by_language_code("tr") or next(iter(yt.captions.values()))
        if caption is None:
            return ""

        xml_captions = caption.xml_captions
        from xml.etree import ElementTree as ET
        root = ET.fromstring(xml_captions)

        full_text = ""
        for text_elem in root.findall('.//text'):
            if text_elem.text:
                full_text += f"{text_elem.text} "

        full_text = clean_text(full_text)
        return full_text
    except Exception:
        return ""

def get_transcript_from_openai(video_id: str) -> str:
    """Placeholder for OpenAI Whisper transcription - currently returns empty string."""
    return ""

def get_transcript(youtube_url: str) -> str:
    """Get transcript from YouTube video using multiple methods."""
    try:
        video_id = extract_video_id(youtube_url)

        transcript = get_transcript_from_youtube_api(video_id)
        if transcript:
            return transcript

        transcript = get_transcript_from_available_languages(video_id)
        if transcript:
            return transcript

        transcript = get_transcript_from_pytube(youtube_url)
        if transcript:
            return transcript

        transcript = get_transcript_from_openai(video_id)
        if transcript:
            return transcript

        return ""

    except Exception:
        return ""

def get_video_info(youtube_url: str) -> Dict[str, Any]:
    """Get video information from YouTube."""
    try:
        yt = YouTube(youtube_url)
        return {
            "title": yt.title,
            "description": yt.description,
            "length": yt.length,
            "views": yt.views,
            "author": yt.author
        }
    except Exception:
        return {}

def analyze_personality(transcript_text: str, person_name: str) -> Dict[str, Any]:
    """Analyze personality from transcript using Agno agent."""
    from agno_agents import PersonalityAnalysisAgent

    agent = PersonalityAnalysisAgent()

    personality = agent.analyze_personality(transcript_text, person_name)

    return personality

def sanitize_name(name: str) -> str:
    """Sanitize name for use as collection name."""
    safe_name = name.replace(' ', '_')

    safe_name = re.sub(r'[^\w\sçğıöşüÇĞİÖŞÜ]', '', safe_name)

    safe_name = re.sub(r'_+', '_', safe_name)

    safe_name = safe_name.strip('_')

    if not safe_name:
        safe_name = "person"

    return safe_name.lower()

def check_collection_exists(collection_name: str) -> bool:
    """Check if a collection exists in Qdrant."""
    try:
        return qdrant_client.collection_exists(collection_name)
    except Exception:
        return False

def create_collection(collection_name: str, vector_size: int = None) -> bool:
    """Create a new collection in Qdrant.

    Args:
        collection_name: Name of the collection to create
        vector_size: Size of the vectors in the collection. If None, uses EMBEDDER_DIMENSIONS from .env

    Returns:
        True if collection was created or already exists
    """
    if vector_size is None:
        vector_size = EMBEDDER_DIMENSIONS

    if check_collection_exists(collection_name):
        return True

    qdrant_client.create_collection(
        collection_name=collection_name,
        vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE)
    )

    return True

def delete_collection(collection_name: str) -> bool:
    """Delete a collection from Qdrant."""
    try:
        if not check_collection_exists(collection_name):
            return True

        qdrant_client.delete_collection(collection_name=collection_name)
        return True
    except Exception:
        return False

def clean_digital_twin_data(digital_twin_id: int, person_name: str, collection_name: str) -> bool:
    """Clean all data related to a digital twin."""
    success = True

    if not delete_collection(collection_name):
        success = False

    try:
        from main import chat_memories, conversation_histories, app

        chat_digital_twin_map = getattr(app.state, 'chat_digital_twin_map', {})

        chat_ids_to_remove = []
        for chat_id, dt_id in chat_digital_twin_map.items():
            if dt_id == digital_twin_id:
                chat_ids_to_remove.append(chat_id)

        for chat_id in chat_ids_to_remove:
            if chat_id in chat_memories:
                del chat_memories[chat_id]
            if chat_id in conversation_histories:
                del conversation_histories[chat_id]
            if chat_id in chat_digital_twin_map:
                del chat_digital_twin_map[chat_id]

        app.state.chat_digital_twin_map = chat_digital_twin_map

    except Exception:
        pass

    return success

def get_embedding(text: str) -> List[float]:
    """Get embedding for text using the Ollama embedding API."""
    try:
        payload = {"model": OLLAMA_EMBEDDER_MODEL, "prompt": text}
        response = requests.post(f"{OLLAMA_URL}/api/embeddings", json=payload)
        response.raise_for_status()

        data = response.json()
        embedding: List[float] = data.get("embeddings") or data.get("embedding") or []
        if not embedding:
            raise ValueError("Empty embedding returned from Ollama")

        if len(embedding) < EMBEDDER_DIMENSIONS:
            embedding.extend([0.0] * (EMBEDDER_DIMENSIONS - len(embedding)))
        elif len(embedding) > EMBEDDER_DIMENSIONS:
            embedding = embedding[:EMBEDDER_DIMENSIONS]

        return embedding
    except Exception as exc:
        raise Exception(f"Failed to generate embedding: {exc}") from exc

def add_to_collection(collection_name: str, text: str, metadata: Dict[str, Any]) -> bool:
    """Add text to Qdrant collection."""
    try:
        embedding = get_embedding(text)

        if not check_collection_exists(collection_name):
            vector_size = len(embedding)
            create_collection(collection_name, vector_size)

        qdrant_client.upsert(
            collection_name=collection_name,
            points=[
                PointStruct(
                    id=str(uuid.uuid4()),
                    vector=embedding,
                    payload={
                        "text": text,
                        **metadata
                    }
                )
            ]
        )
        return True
    except Exception as e:
        raise Exception(f"Failed to add to collection: {str(e)}")

def search_collection(collection_name: str, query: str, limit: int = 5) -> List[Dict[str, Any]]:
    """Search Qdrant collection."""
    try:
        embedding = get_embedding(query)

        if not check_collection_exists(collection_name):
            return []

        results = qdrant_client.search(
            collection_name=collection_name,
            query_vector=embedding,
            limit=limit
        )

        formatted_results = []
        for result in results:
            formatted_results.append({
                "text": result.payload.get("text", ""),
                "metadata": {k: v for k, v in result.payload.items() if k != "text"},
                "score": result.score
            })

        return formatted_results
    except Exception as e:
        raise Exception(f"Failed to search collection: {str(e)}")

def delete_document_from_collection(collection_name: str, document_id: int) -> bool:
    """Delete all points related to a specific document from Qdrant collection."""
    try:
        if not check_collection_exists(collection_name):
            return True

        from qdrant_client.models import Filter, FieldCondition, MatchValue

        filter_condition = Filter(
            must=[
                FieldCondition(
                    key="document_id",
                    match=MatchValue(value=document_id)
                )
            ]
        )

        search_result = qdrant_client.scroll(
            collection_name=collection_name,
            scroll_filter=filter_condition,
            limit=1000,
            with_payload=True,
            with_vectors=False
        )

        points_to_delete = []
        for point in search_result[0]:
            points_to_delete.append(point.id)

        if not points_to_delete:
            return True

        qdrant_client.delete(
            collection_name=collection_name,
            points_selector=points_to_delete
        )

        return True

    except Exception as e:
        return False

def extract_text_from_docx(file_content: bytes) -> str:
    """Extract text from a Word document - Legacy function, now uses advanced parser."""
    return extract_text_from_document(file_content, "document.docx")

def extract_text_from_pdf(file_content: bytes) -> str:
    """Extract text from a PDF document - Legacy function, now uses advanced parser."""
    return extract_text_from_document(file_content, "document.pdf")

def extract_text_from_any_document(file_content: bytes, filename: str) -> str:
    """
    Geli�mi� dok�man parser ile herhangi bir dok�man t�r�nden metin ��kar
    
    Desteklenen formatlar:
    - Office: .docx, .doc, .xlsx, .xls, .pptx, .ppt
    - PDF: .pdf (OCR deste�i ile)
    - Resimler: .png, .jpg, .jpeg, .tiff, .bmp, .gif (OCR)
    - Metin: .txt, .md, .rst, .rtf
    - Web: .html, .htm, .xml
    - Veri: .json, .csv, .tsv
    - E-kitap: .epub
    - LibreOffice: .odt, .ods, .odp
    """
    try:
        # Yeni geli�mi� parser'� kullan
        text = extract_text_from_document(file_content, filename)
        
        if text and text.strip():
            return text.strip()
        else:
            # Fallback: eski parser'lar� dene
            if filename.lower().endswith('.docx'):
                return extract_text_from_docx_legacy(file_content)
            elif filename.lower().endswith('.pdf'):
                return extract_text_from_pdf_legacy(file_content)
            else:
                return ""
                
    except Exception as e:
        print(f"Geli�mi� parsing hatas�: {e}")
        # Fallback: eski parser'lar� dene
        try:
            if filename.lower().endswith('.docx'):
                return extract_text_from_docx_legacy(file_content)
            elif filename.lower().endswith('.pdf'):
                return extract_text_from_pdf_legacy(file_content)
            else:
                return ""
        except Exception as fallback_e:
            print(f"Fallback parsing de ba�ar�s�z: {fallback_e}")
            return ""

def extract_text_from_docx_legacy(file_content: bytes) -> str:
    """Legacy DOCX parser - fallback ama�l�."""
    try:
        doc = docx.Document(BytesIO(file_content))
        full_text = []

        for para in doc.paragraphs:
            if para.text.strip():
                full_text.append(para.text)

        return "\n".join(full_text)
    except Exception:
        return ""

def extract_text_from_pdf_legacy(file_content: bytes) -> str:
    """Legacy PDF parser - fallback ama�l�."""
    try:
        pdf_reader = PyPDF2.PdfReader(BytesIO(file_content))
        full_text = []

        for page in pdf_reader.pages:
            full_text.append(page.extract_text())

        return "\n".join(full_text)
    except Exception:
        return ""

def clean_text(text: str) -> str:
    """Clean text by removing URLs, HTML tags, and extra whitespace."""
    if not text:
        return ""
    text = re.sub(r'<[^>]+>', '', text)
    text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
    text = re.sub(r'\s+', ' ', text).strip()
    text = re.sub(r'httpsli\S+', '', text)
    return text

def split_text_into_chunks(text: str, chunk_size: int = 1000, chunk_overlap_ratio: float = 0.15) -> List[str]:
    """
    Metni anlamsal olarak daha küçük parçalara böler (Semantic Chunker).
    Cümle sınırlarına saygı duyar ve parçalar arasında bağlamı korumak için
    bir miktar kesişim (overlap) bırakır.
    """
    if not text or not text.strip():
        return []

    # LlamaIndex'in anlamsal bölücüsünü başlat
    # Bu bölücü, metni cümlelere ayırır ve ardından bu cümleleri chunk_size'ı aşmayacak şekilde birleştirir.
    splitter = SentenceSplitter(
        chunk_size=chunk_size,
        chunk_overlap=int(chunk_size * chunk_overlap_ratio),  # Kesişim miktarını chunk boyutuna oranla belirle
        separator=" ", # Cümleler arası varsayılan ayırıcı
        paragraph_separator="\n\n", # Paragraf ayırıcılarına öncelik ver
    )

    # Metni böl
    chunks = splitter.split_text(text)
    
    # Sonuçta oluşabilecek boş chunk'ları temizle
    return [chunk for chunk in chunks if chunk.strip()]


class Memory:
    """Memory class for storing conversation history."""

    def __init__(self, max_items: int = 50):
        self.max_items = max_items
        self.items = []

    def add(self, user_input: str, bot_response: str, importance: float = 1.0):
        """Add a new memory item."""
        item = {
            "user_input": user_input,
            "bot_response": bot_response,
            "importance": importance,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        self.items.append(item)

        if len(self.items) > self.max_items:
            self.items.sort(key=lambda x: x["importance"])
            self.items = self.items[-self.max_items:]

    def get_relevant(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Get relevant memory items."""
        scored_items = []
        query_words = set(query.lower().split())

        for item in self.items:
            text = f"{item['user_input']} {item['bot_response']}".lower()
            text_words = set(text.split())

            common_words = query_words.intersection(text_words)
            score = len(common_words) / max(len(query_words), 1)

            scored_items.append((item, score))

        scored_items.sort(key=lambda x: x[1], reverse=True)
        return [item for item, _ in scored_items[:limit]]

    def get_recent(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get most recent memory items."""
        return self.items[-limit:] if self.items else []

def generate_system_message(personality: Dict[str, Any], person_name: str) -> str:
    """Generate system message for chat."""
    konusma_tarzi = personality.get("konusma_tarzi", {})
    genel_ton = konusma_tarzi.get("genel_ton", "")
    cumle_yapisi = konusma_tarzi.get("cumle_yapisi", "")
    kelime_secimi = konusma_tarzi.get("kelime_secimi", "")
    dil_ozellikleri = konusma_tarzi.get("dil_ozellikleri", [])

    sik_kullanilan_ifadeler = personality.get("sik_kullanilan_ifadeler", [])
    bilgi_alanlari = personality.get("bilgi_alanlari", [])

    dusunce_yapisi = personality.get("dusunce_yapisi", {})
    yaklasim = dusunce_yapisi.get("yaklasim", "")
    analiz_sekli = dusunce_yapisi.get("analiz_sekli", "")
    karar_verme = dusunce_yapisi.get("karar_verme", "")

    kisilik_ozellikleri = personality.get("kisilik_ozellikleri", [])

    system_message = f"""
    Sen {person_name} olarak davranacaksın. Aşağıdaki özelliklere sahipsin:

    KONUşMA TARZIN:
    - Genel ton: {genel_ton}
    - Cümle yapısı: {cumle_yapisi}
    - Kelime seçimi: {kelime_secimi}
    - Dil özellikleri: {', '.join(dil_ozellikleri)}

    SIK KULLANDIğIN İFADELER:
    {', '.join(sik_kullanilan_ifadeler)}

    BİLGİ ALANLARIN:
    {', '.join(bilgi_alanlari)}

    DÜşÜNCE YAPIN:
    - Yaklaşım: {yaklasim}
    - Analiz şekli: {analiz_sekli}
    - Karar verme: {karar_verme}

    KİşİLİK ÖZELLİKLERİN:
    {', '.join(kisilik_ozellikleri)}

    ÖNEMLİ: Sen {person_name}'sın. Asla kendinden "yapay zeka", "AI", "model", "dijital ikiz" veya benzeri terimlerle bahsetme. Her zaman birinci tekil şahıs olarak konuş ("Ben {person_name}").

    "Sen kimsin?" gibi kimlik sorularına doğrudan "Ben {person_name}" diye cevap ver ve kişiliğini yansıt. Asla "Ben bir dijital ikizim" veya "Ben bir yapay zekayım" gibi cevaplar verme.

    Kullanıcının sorularına kendi ağzından, kendi konuşma tarzını, düşünce yapını ve kişilik özelliklerini yansıtarak cevap ver. Cevaplarında sık kullandığın ifadeleri kullan ve bilgi alanlarındaki uzmanlığını yansıt.

    Bilmediğin konularda uydurma, bilmediğini söyle. Sana verilen bilgileri kullanarak yanıt oluştur.

    Önceki konuşmaları hatırla ve tutarlı yanıtlar ver. Kullanıcının önceki sorularına ve senin verdiğin yanıtlara atıfta bulun.

    Kişiliğini ve karakterini tam olarak yansıt. Gerçek {person_name} gibi davran.
    """

    return system_message

def extract_and_embed_text_from_youtube(text: str, person_name: str, collection_name: str) -> bool:
    """
    YouTube video transkriptini par�alara b�ler ve Qdrant koleksiyonuna ekler.

    Args:
        text: YouTube video transkripti
        person_name: Ki�inin ad�
        collection_name: Qdrant koleksiyon ad�

    Returns:
        bool: ��lem ba�ar�l� ise True, de�ilse False
    """
    try:
        cleaned_text = clean_text(text)
        if not cleaned_text:
            return False

        # Koleksiyonu   n var oldu�undan emin ol
        if not check_collection_exists(collection_name):
            create_collection(collection_name)

        # Metni par�alara b�l
        chunks = split_text_into_chunks(cleaned_text, chunk_size=1000)

        # Her par�ay� koleksiyona ekle
        success_count = 0
        for i, chunk in enumerate(chunks):
            try:
                metadata = {
                    "person_name": person_name,
                    "chunk_index": i,
                    "total_chunks": len(chunks),
                    "source": "youtube_transcript",
                    "timestamp": str(datetime.now())
                }

                success = add_to_collection(collection_name, chunk, metadata)
                if success:
                    success_count += 1

            except Exception as e:
                pass

        # En az yar�s� ba�ar�l� ise True d�nd�r
        return success_count >= len(chunks) / 2

    except Exception as e:
        return False

def chat_with_digital_twin(
    user_input: str,
    person_name: str,
    collection_name: str,
    personality: Dict[str, Any],
    memory: Memory,
    conversation_history: List[Dict[str, str]] = None
) -> str:
    """Chat with digital twin using Agno Agent."""
    try:
        if not isinstance(personality, dict):
            personality = {}

        if conversation_history is None:
            system_message = generate_system_message(personality, person_name)
            conversation_history = [
                {"role": "system", "content": system_message}
            ]

        if not check_collection_exists(collection_name):
            create_collection(collection_name)

        from agno_agents import DigitalTwinAgent

        agent = DigitalTwinAgent(
            person_name=person_name,
            personality=personality,
            
        )

        bot_response = agent.get_response(user_input)

        memory.add(user_input, bot_response)

        if hasattr(agent, " conversation_history") and agent.conversation_history:
            conversation_history = agent.conversation_history

        return bot_response
    except Exception as e:
        return f"�zg�n�m, bir hata olu�tu: {str(e)}"
