from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
import json
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone

from database import DigitalTwin, Document, Chat, Message
from digital_twin_utils import sanitize_name
from schemas import DigitalTwinCreate, DigitalTwinUpdate

# Dijital ikiz CRUD işlemleri
def create_digital_twin(db: Session, digital_twin: DigitalTwinCreate, user_id: int) -> DigitalTwin:
    """Yeni dijital ikiz oluştur."""
    sanitized_name = sanitize_name(digital_twin.name)
    collection_name = sanitized_name + "_collection"

    db_digital_twin = DigitalTwin(
        user_id=user_id,
        name=digital_twin.name,
        sanitized_name=sanitized_name,
        collection_name=collection_name,
        youtube_url=digital_twin.youtube_url,
        language="tr",
        status="processing"
    )

    try:
        db.add(db_digital_twin)
        db.commit()
        db.refresh(db_digital_twin)
        return db_digital_twin
    except IntegrityError:
        db.rollback()
        raise ValueError(f"Digital twin with name '{digital_twin.name}' already exists")

def get_digital_twin(db: Session, digital_twin_id: int) -> Optional[DigitalTwin]:
    """ID'ye göre dijital ikiz getir."""
    return db.query(DigitalTwin).filter(DigitalTwin.id == digital_twin_id).first()

def get_digital_twin_by_sanitized_name(db: Session, sanitized_name: str) -> Optional[DigitalTwin]:
    """Temizlenmiş isme göre dijital ikiz getir."""
    return db.query(DigitalTwin).filter(DigitalTwin.sanitized_name == sanitized_name).first()

def get_digital_twins(db: Session, skip: int = 0, limit: int = 100) -> List[DigitalTwin]:
    """Tüm dijital ikizleri getir."""
    return db.query(DigitalTwin).offset(skip).limit(limit).all()

def update_digital_twin(db: Session, digital_twin_id: int, digital_twin: DigitalTwinUpdate) -> Optional[DigitalTwin]:
    """Dijital ikiz güncelle."""
    db_digital_twin = get_digital_twin(db, digital_twin_id)
    if not db_digital_twin:
        return None

    update_data = digital_twin.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_digital_twin, key, value)

    db.commit()
    db.refresh(db_digital_twin)
    return db_digital_twin

def update_digital_twin_status(db: Session, digital_twin_id: int, status: str) -> Optional[DigitalTwin]:
    """Dijital ikiz durumunu güncelle."""
    db_digital_twin = get_digital_twin(db, digital_twin_id)
    if not db_digital_twin:
        return None

    db_digital_twin.status = status
    db_digital_twin.updated_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(db_digital_twin)
    return db_digital_twin

def update_digital_twin_personality(db: Session, digital_twin_id: int, personality: Dict[str, Any]) -> Optional[DigitalTwin]:
    """Dijital ikiz kişilik verilerini güncelle."""
    db_digital_twin = get_digital_twin(db, digital_twin_id)
    if not db_digital_twin:
        return None

    db_digital_twin.personality = json.dumps(personality)
    db_digital_twin.updated_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(db_digital_twin)
    return db_digital_twin

def delete_digital_twin(db: Session, digital_twin_id: int) -> bool:
    """Dijital ikiz sil."""
    db_digital_twin = get_digital_twin(db, digital_twin_id)
    if not db_digital_twin:
        return False

    db.delete(db_digital_twin)
    db.commit()
    return True

# Doküman CRUD işlemleri
def create_document(db: Session, digital_twin_id: int, filename: str, content: str = "") -> Optional[Document]:
    """
    Yeni doküman kaydı oluştur. Başlangıçta içerik boş olabilir.
    Durum, işlenmek üzere "queued" (sırada) olarak ayarlanır.
    """
    db_digital_twin = get_digital_twin(db, digital_twin_id)
    if not db_digital_twin:
        return None

    db_document = Document(
        digital_twin_id=digital_twin_id,
        filename=filename,
        content=content,
        status="queued" # Yeni varsayılan durum
    )

    db.add(db_document)
    db.commit()
    db.refresh(db_document)
    return db_document

def get_document(db: Session, document_id: int) -> Optional[Document]:
    """ID'ye göre doküman getir."""
    return db.query(Document).filter(Document.id == document_id).first()

def get_documents_by_ids(db: Session, document_ids: List[int]) -> List[Document]:
    """Verilen ID listesindeki tüm dokümanları tek bir sorguda getir."""
    if not document_ids:
        return []
    return db.query(Document).filter(Document.id.in_(document_ids)).all()

def get_documents_by_digital_twin(db: Session, digital_twin_id: int) -> List[Document]:
    """Dijital ikize ait tüm dokümanları getir."""
    return db.query(Document).filter(Document.digital_twin_id == digital_twin_id).all()

def update_document_content_and_status(db: Session, document_id: int, content: str, status: str) -> Optional[Document]:
    """Dokümanın ayrıştırılmış içeriğini ve durumunu güncelle."""
    db_document = get_document(db, document_id)
    if not db_document:
        return None

    db_document.content = content
    db_document.status = status
    db_document.updated_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(db_document)
    return db_document

def get_all_documents(db: Session) -> List[Document]:
    """Tüm dokümanları getir."""
    return db.query(Document).order_by(Document.created_at.desc()).all()

def update_document_status(db: Session, document_id: int, status: str) -> Optional[Document]:
    """Doküman durumunu güncelle."""
    db_document = get_document(db, document_id)
    if not db_document:
        return None

    db_document.status = status
    db_document.updated_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(db_document)
    return db_document

def update_document_progress(db: Session, document_id: int, progress: int) -> Optional[Document]:
    """Doküman işleme ilerlemesini güncelle."""
    db_document = get_document(db, document_id)
    if not db_document:
        return None

    db_document.progress = max(0, min(100, progress))  # 0-100 arası sınırla
    db_document.updated_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(db_document)
    return db_document

def update_document_status_and_progress(db: Session, document_id: int, status: str, progress: int) -> Optional[Document]:
    """Doküman durumu ve ilerlemesini birlikte güncelle."""
    db_document = get_document(db, document_id)
    if not db_document:
        return None

    db_document.status = status
    db_document.progress = max(0, min(100, progress))
    db_document.updated_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(db_document)
    return db_document

def delete_document(db: Session, document_id: int) -> bool:
    """Doküman sil."""
    db_document = get_document(db, document_id)
    if not db_document:
        return False

    db.delete(db_document)
    db.commit()
    return True
