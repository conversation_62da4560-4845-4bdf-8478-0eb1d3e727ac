#!/usr/bin/env python3
"""
OCR Model İndirme ve Kontrol Scripti
Docker build sırasında OCR modellerini önceden indirir
"""

import os
import time
import logging
from pathlib import Path

# Logging konfigürasyonu
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_and_download_ocr_models():
    """OCR modellerinin varlığını kontrol et ve gerekirse indir"""
    try:
        logger.info("OCR modelleri kontrol ediliyor...")
        
        # EasyOCR model klasörünü kontrol et
        easycr_dir = Path.home() / '.EasyOCR' / 'model'
        
        # Model dosyalarını kontrol et (Türkçe için gerekli dosyalar)
        detection_model = easycr_dir / 'craft_mlt_25k.pth'
        
        # Türkçe recognition model'i farklı isimlerle olabilir
        possible_tr_models = [
            easycr_dir / 'turkish_g2.pth',
            easycr_dir / 'latin_g2.pth',
            easycr_dir / 'tr.pth'
        ]
        
        # Mevcut Türkçe modelini bul
        existing_tr_model = None
        for model_path in possible_tr_models:
            if model_path.exists() and model_path.stat().st_size > 0:
                existing_tr_model = model_path
                break
        
        # Dosyalar varsa ve boş değilse
        if detection_model.exists() and detection_model.stat().st_size > 0 and existing_tr_model:
            logger.info("✅ OCR modelleri zaten mevcut!")
            logger.info(f"Detection model: {detection_model} ({detection_model.stat().st_size} bytes)")
            logger.info(f"Recognition model: {existing_tr_model} ({existing_tr_model.stat().st_size} bytes)")
            logger.info("✅ Model indirme tamamlandı!")
            return True
        
        logger.info("OCR modelleri bulunamadı, indiriliyor...")
        
        # EasyOCR'ı import et
        import easyocr
        
        # Sadece modelleri indir, Reader'ı saklamayalım (memory tasarrufu için)
        logger.info("Türkçe OCR modeli indiriliyor (CPU-only mode)...")
        
        # GPU kullanmadan sadece CPU ile model indir
        try:
            logger.info("CPU ile model indiriliyor...")
            # Reader oluştur ama hemen sil - GPU=False zorla
            reader = easyocr.Reader(['tr'], gpu=False, verbose=True, download_enabled=True)
            del reader  # Memory'den temizle
            logger.info("✅ Modeller CPU ile indirildi!")
        except Exception as cpu_error:
            logger.error(f"❌ CPU ile model indirme hatası: {cpu_error}")
            raise
        
        # Kısa bir bekleme - model dosyalarının yazılması için
        time.sleep(2)
        
        logger.info("OCR modelleri başarıyla indirildi!")
        logger.info("Model dosyaları ~/.EasyOCR/ klasöründe saklandı")
        
        # İndirilen Türkçe modelini bul
        downloaded_tr_model = None
        for model_path in possible_tr_models:
            if model_path.exists() and model_path.stat().st_size > 0:
                downloaded_tr_model = model_path
                break
        
        # Model dosyalarının indirildiğini doğrula
        if detection_model.exists() and detection_model.stat().st_size > 0 and downloaded_tr_model:
            logger.info("✅ Tüm gerekli model dosyaları başarıyla indirildi!")
            logger.info(f"Detection model: {detection_model} ({detection_model.stat().st_size} bytes)")
            logger.info(f"Recognition model: {downloaded_tr_model} ({downloaded_tr_model.stat().st_size} bytes)")
            return True
        else:
            logger.error("❌ Model dosyaları indirilemedi veya eksik!")
            return False
            
    except Exception as e:
        logger.error(f"❌ OCR model indirme hatası: {e}")
        return False

if __name__ == "__main__":
    """Ana çalıştırma bloğu"""
    success = check_and_download_ocr_models()
    if not success:
        exit(1)  # Docker build'inin başarısız olması için
    exit(0) 
