from datetime import datetime, timedelta, timezone
from typing import Optional
import jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from database import get_db, User
import os
import hashlib
from dotenv import load_dotenv

load_dotenv()

# JWT ayarları
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-this-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 24 * 60  # 24 saat
REFRESH_TOKEN_EXPIRE_DAYS = 30  # 30 gün

# Şifre hashleme context'i
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer token güvenlik şeması
security = HTTPBearer()

def simple_hash_password(password: str) -> str:
    """SHA256 ile basit şifre hashleme."""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Şifre doğrulama - hem bcrypt hem SHA256 destekli."""
    try:
        # Önce bcrypt ile dene
        result = pwd_context.verify(plain_password, hashed_password)
        if result:
            return True
    except Exception as e:
        pass
    
    # Bcrypt başarısızsa SHA256 ile dene (eski şifreler için)
    simple_hash = simple_hash_password(plain_password)
    sha_result = simple_hash == hashed_password
    return sha_result

def get_password_hash(password: str) -> str:
    """Bcrypt ile şifre hashleme."""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """JWT access token oluşturma."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def create_refresh_token(data: dict):
    """JWT refresh token oluşturma."""
    to_encode = data.copy()
    expire = datetime.now(timezone.utc) + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str, token_type: str = "access") -> Optional[dict]:
    """JWT token doğrulama ve decode etme."""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        if payload.get("type") != token_type:
            return None
        return payload
    except jwt.PyJWTError:
        return None

# Veritabanı kullanıcı işlemleri
def get_user_by_username(db: Session, username: str) -> Optional[User]:
    """Kullanıcı adına göre kullanıcı getir."""
    user = db.query(User).filter(User.username == username).first()
    return user

def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
    """ID'ye göre kullanıcı getir."""
    return db.query(User).filter(User.id == user_id).first()

def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    """Kullanıcı kimlik doğrulama."""
    user = get_user_by_username(db, username)
    if not user:
        return None
    
    if not verify_password(password, user.hashed_password):
        return None
    
    return user

def create_user(db: Session, username: str, password: str, email: Optional[str] = None, role: str = "user") -> User:
    """Yeni kullanıcı oluşturma."""
    if get_user_by_username(db, username):
        raise ValueError("Username already exists")

    if email and db.query(User).filter(User.email == email).first():
        raise ValueError("Email already exists")

    hashed_password = get_password_hash(password)
    db_user = User(
        username=username,
        email=email,
        hashed_password=hashed_password,
        role=role,
        is_admin=(role == "admin")
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

# FastAPI dependency'leri
async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """Mevcut kimlik doğrulanmış kullanıcıyı getir."""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        token = credentials.credentials
        payload = verify_token(token)
        if payload is None:
            raise credentials_exception
        
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
            
    except Exception:
        raise credentials_exception
    
    user = get_user_by_username(db, username)
    if user is None:
        raise credentials_exception
    
    return user

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Mevcut aktif kullanıcıyı getir."""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

async def get_admin_user(current_user: User = Depends(get_current_active_user)) -> User:
    """Admin yetkisi kontrolü."""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=403,
            detail="Access denied: Admin privileges required"
        )
    
    return current_user
