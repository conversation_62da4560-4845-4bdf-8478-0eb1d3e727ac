from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime
import re


# Kullanıcı yönetimi şemaları
class UserBase(BaseModel):
    username: str
    email: Optional[str] = None

class UserCreate(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=6, max_length=100)
    email: Optional[str] = None

class UserLogin(BaseModel):
    username: str
    password: str

class UserResponse(BaseModel):
    id: int
    username: str
    email: Optional[str]
    role: str
    is_admin: bool
    is_active: bool
    created_at: datetime

    class Config:
        from_attributes = True

# JWT token şemaları
class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    username: Optional[str] = None


# Dijital ikiz şemaları
class DigitalTwinBase(BaseModel):
    name: str
    youtube_url: str
    language: str = "tr"  # Always Turkish

class DigitalTwinCreate(DigitalTwinBase):
    @validator('name')
    def name_must_not_be_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('Name cannot be empty')
        return v

    @validator('youtube_url')
    def youtube_url_must_be_valid(cls, v):
        # YouTube URL doğrulama
        if not re.match(r'^(https?://)?(www\.)?(youtube\.com|youtu\.be)/.+$', v):
            raise ValueError('Invalid YouTube URL')
        return v

class DigitalTwinUpdate(BaseModel):
    name: Optional[str] = None
    youtube_url: Optional[str] = None
    language: Optional[str] = None
    status: Optional[str] = None

class DigitalTwinInDB(DigitalTwinBase):
    id: int
    sanitized_name: str
    collection_name: str
    status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class DigitalTwinResponse(DigitalTwinBase):
    id: str
    collection_name: str
    status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

    @classmethod
    def from_orm(cls, obj):
        # Integer ID'yi string'e çevir
        obj_dict = {
            "id": str(obj.id),
            "name": obj.name,
            "youtube_url": obj.youtube_url,
            "language": obj.language,
            "collection_name": obj.collection_name,
            "status": obj.status,
            "created_at": obj.created_at,
            "updated_at": obj.updated_at
        }
        return cls(**obj_dict)


# Doküman şemaları
class DocumentBase(BaseModel):
    digital_twin_id: str

class DocumentCreate(DocumentBase):
    pass

class DocumentInDB(DocumentBase):
    id: int
    filename: str
    status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class DocumentResponse(DocumentBase):
    id: str
    filename: str
    status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

    @classmethod
    def from_orm(cls, obj):
        obj_dict = {
            "id": str(obj.id),
            "digital_twin_id": obj.digital_twin_id,
            "filename": obj.filename,
            "status": obj.status,
            "created_at": obj.created_at,
            "updated_at": obj.updated_at
        }
        return cls(**obj_dict)


# Chat şemaları
class ChatBase(BaseModel):
    digital_twin_id: str

class ChatCreate(ChatBase):
    pass

class ChatInDB(ChatBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ChatResponse(ChatBase):
    id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

    @classmethod
    def from_orm(cls, obj):
        obj_dict = {
            "id": str(obj.id),
            "digital_twin_id": obj.digital_twin_id,
            "created_at": obj.created_at,
            "updated_at": obj.updated_at
        }
        return cls(**obj_dict)


# Mesaj şemaları
class MessageBase(BaseModel):
    content: str

class MessageCreate(MessageBase):
    pass

class MessageInDB(MessageBase):
    id: int
    chat_id: int
    is_user: bool
    created_at: datetime

    class Config:
        from_attributes = True

class MessageResponse(MessageBase):
    id: str
    chat_id: str
    is_user: bool
    created_at: datetime

    class Config:
        from_attributes = True

    @classmethod
    def from_orm(cls, obj):
        obj_dict = {
            "id": str(obj.id),
            "chat_id": str(obj.chat_id),
            "is_user": obj.is_user,
            "content": obj.content,
            "created_at": obj.created_at
        }
        return cls(**obj_dict)

# Chat mesaj modeli (basit)
class ChatMessage(BaseModel):
    content: str
