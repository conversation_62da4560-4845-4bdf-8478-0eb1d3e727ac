FROM python:3.10-slim

# OCR ve doküman işleme için sistem bağımlılıklarını yükle
RUN apt-get update && apt-get install -y \
    libmagic1 \
    libmagic-dev \
    poppler-utils \
    tesseract-ocr \
    tesseract-ocr-tur \
    tesseract-ocr-eng \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    wget \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY requirements.txt .

# Python paketlerini yükle
RUN pip install --no-cache-dir -r requirements.txt

# OCR modellerini önceden indir (Docker build sırasında)
COPY init_ocr_models.py .
RUN python init_ocr_models.py

COPY . .

EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--timeout-keep-alive", "86400", "--timeout-graceful-shutdown", "86400"]
