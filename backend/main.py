# Digital Twin System - FastAPI Backend
# Ana API sunucusu ve endpoint'ler
import asyncio
import json
import time
from contextlib import asynccontextmanager
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any

from fastapi import FastAPI, Depends, HTTPException, UploadFile, File, Form, BackgroundTasks, Body
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from sqlalchemy.orm import Session
from sqlalchemy.exc import OperationalError

# Auth ve database imports
from auth import get_current_active_user, get_admin_user
from database import get_db, get_db_session, User
from digital_twin_db import (
    create_digital_twin as db_create_digital_twin,
    get_digital_twin as db_get_digital_twin,
    get_digital_twins as db_get_digital_twins,
    update_digital_twin_status,
    update_digital_twin_personality,
    create_document,
    get_document as db_get_document,
    get_documents_by_digital_twin as db_get_documents_by_digital_twin,
    delete_document as db_delete_document,
    get_documents_by_ids
)
from digital_twin_utils import (
    get_transcript,
    analyze_personality,
    create_collection,
    delete_collection,
    check_collection_exists,
    extract_text_from_docx,
    extract_text_from_pdf,
    extract_text_from_any_document,
    add_to_collection,
    clean_text,
    split_text_into_chunks,
    chat_with_digital_twin,
    sanitize_name,
    clean_digital_twin_data,
    delete_document_from_collection,
    Memory
)
from document_parser import get_supported_file_extensions, is_supported_file
from schemas import (
    UserCreate, UserResponse, UserLogin, Token,
    DigitalTwinCreate, DigitalTwinResponse,
    DocumentResponse
)

def wait_for_db():
    """Veritabanı hazır olana kadar bekle"""
    from database import SessionLocal
    from sqlalchemy.exc import OperationalError
    from sqlalchemy import text
    
    retries = 10
    delay = 5  # saniye
    
    for i in range(retries):
        try:
            db = SessionLocal()
            # Basit bir sorgu ile bağlantıyı test et
            db.execute(text("SELECT 1"))
            db.close()
            print("✅ Veritabanı bağlantısı başarılı!")
            return
        except OperationalError as e:
            print(f"Veritabanı hazır değil, {delay} saniye bekleniyor... ({i+1}/{retries})")
            print(f"Hata: {e}")
            time.sleep(delay)
    
    print("❌ Veritabanına bağlanılamadı!")
    raise Exception("Database connection failed after multiple retries")


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Sistem başlatma
    try:
        print("🚀 Digital Twin System başlatılıyor...")

        # Veritabanının hazır olmasını bekle
        wait_for_db()

        # Veritabanı tablolarını oluştur
        from database import create_tables
        create_tables()
        print("📊 Veritabanı hazır")

        # OCR parser hazırla
        try:
            import os
            from document_parser import AdvancedDocumentParser
            global ocr_parser
            ocr_parser = AdvancedDocumentParser()
            # OCR zaten constructor'da yüklendi
            print("✅ Sistem ve OCR hazır")
        except Exception as e:
            print(f"❌ Hata: {e}")
            ocr_parser = None

        # Admin kullanıcısının varlığını garanti et
        with get_db_session() as db:
            from database import User
            from auth import get_password_hash

            admin_user = db.query(User).filter(User.username == "admin").first()
            if not admin_user:
                print("Creating default admin user (admin / admin123)...")
                hashed_password = get_password_hash("admin123")
                admin_user = User(
                    username="admin",
                    email="admin@localhost",
                    hashed_password=hashed_password,
                    role="admin",
                    is_admin=True,
                    is_active=True,
                )
                db.add(admin_user)
                db.commit()
                print("Admin user created successfully")
            else:
                # Admin kullanıcısının role ve is_admin değerlerinin doğru olduğundan emin ol
                needs_update = False
                if admin_user.role != "admin":
                    admin_user.role = "admin"
                    needs_update = True
                if not admin_user.is_admin:
                    admin_user.is_admin = True
                    needs_update = True
                if needs_update:
                    db.commit()
                    print("Admin user permissions updated")
    except Exception as e:
        print(f"Admin user initialization error: {e}")

    yield

    # Sistem kapatma
    print("Digital Twin System shutting down")

# Global OCR parser
ocr_parser = None

# FastAPI uygulaması oluştur
app = FastAPI(lifespan=lifespan)

# CORS middleware ekle
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Memory storage for chats
chat_memories: Dict[str, List[Dict[str, str]]] = {}
conversation_histories: Dict[str, List[Dict[str, str]]] = {}

# Health check endpoint
@app.get("/health")
async def health_check():
    """Sistem durumu kontrolü - OCR ve veritabanı durumunu kontrol eder"""
    global ocr_parser

    # Veritabanı sağlık kontrolü
    from database import check_db_health
    db_healthy, db_message = check_db_health()

    if ocr_parser is None:
        return {
            "status": "error" if not db_healthy else "partial",
            "ocr_ready": False,
            "db_healthy": db_healthy,
            "message": f"OCR parser hazırlanamadı. DB: {db_message}"
        }

    ocr_ready = ocr_parser.ocr_reader is not None

    # OCR device bilgisi
    device = None
    if ocr_ready:
        try:
            import torch
            device = "GPU" if torch.cuda.is_available() else "CPU"
        except:
            device = "CPU"

    overall_status = "healthy" if (ocr_ready and db_healthy) else ("partial" if db_healthy else "error")

    return {
        "status": overall_status,
        "ocr_ready": ocr_ready,
        "ocr_device": device,
        "db_healthy": db_healthy,
        "db_message": db_message,
        "message": f"OCR: {'Ready' if ocr_ready else 'Not Ready'} ({device if device else 'N/A'}), DB: {db_message}"
    }

# Pydantic modeller (API request/response için)
class DigitalTwinCreate(BaseModel):
    name: str
    youtube_url: str

class DigitalTwinResponse(BaseModel):
    id: str
    name: str
    collection_name: str
    youtube_url: Optional[str] = None
    created_at: datetime
    status: Optional[str] = "processing"

class DocumentUpload(BaseModel):
    digital_twin_id: str

class DocumentResponse(BaseModel):
    id: str
    digital_twin_id: str
    filename: str
    status: str
    created_at: datetime

# Authentication endpoints
@app.post("/auth/register", response_model=UserResponse)
async def register(user_data: UserCreate, db: Session = Depends(get_db)):
    """Yeni kullanıcı kaydı"""
    from auth import get_password_hash, get_user_by_username
    
    existing_user = get_user_by_username(db, user_data.username)
    if existing_user:
        raise HTTPException(status_code=400, detail="Username already registered")
    
    hashed_password = get_password_hash(user_data.password)
    db_user = User(
        username=user_data.username,
        email=user_data.email,
        hashed_password=hashed_password
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return UserResponse(
        id=str(db_user.id), 
        username=db_user.username, 
        email=db_user.email, 
        role=db_user.role,
        is_admin=db_user.is_admin,
        is_active=db_user.is_active,
        created_at=db_user.created_at
    )

@app.post("/auth/login", response_model=Token)
async def login(user_data: UserLogin, db: Session = Depends(get_db)):
    from auth import authenticate_user, create_access_token
    
    user = authenticate_user(db, user_data.username, user_data.password)
    if not user:
        raise HTTPException(status_code=401, detail="Incorrect username or password")
    
    access_token = create_access_token(data={"sub": user.username})
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/auth/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    return UserResponse(
        id=str(current_user.id), 
        username=current_user.username,
        email=current_user.email,
        role=current_user.role,
        is_admin=current_user.is_admin,
        is_active=current_user.is_active,
        created_at=current_user.created_at
    )

class ChatMessage(BaseModel):
    content: str

class ChatResponse(BaseModel):
    id: str
    digital_twin_id: str
    created_at: datetime

class MessageResponse(BaseModel):
    id: str
    chat_id: str
    is_user: bool
    content: str
    created_at: datetime

@app.post("/digital-twins", response_model=DigitalTwinResponse)
async def create_digital_twin(
    digital_twin: DigitalTwinCreate,
    background_tasks: BackgroundTasks,
    admin_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    db_digital_twin = None
    collection_name = None

    try:
        db_digital_twin = db_create_digital_twin(db, digital_twin, admin_user.id)

        collection_name = db_digital_twin.collection_name

        create_collection(collection_name)

        background_tasks.add_task(
            process_digital_twin,
            db_digital_twin.id,
                    digital_twin.name,
        digital_twin.youtube_url,
        collection_name
        )

        response_data = {
            "id": str(db_digital_twin.id),
            "name": db_digital_twin.name,
            "collection_name": db_digital_twin.collection_name,
            "youtube_url": db_digital_twin.youtube_url,
            "created_at": db_digital_twin.created_at,
            "status": db_digital_twin.status
        }

        return DigitalTwinResponse(**response_data)
    
    except ValueError as e:
        if collection_name:
            delete_collection(collection_name)

        if db_digital_twin and db_digital_twin.id:
            try:
                db.delete(db_digital_twin)
                db.commit()
            except Exception as db_error:
                pass

        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        if collection_name:
            delete_collection(collection_name)

        if db_digital_twin and db_digital_twin.id:
            try:
                db.delete(db_digital_twin)
                db.commit()
            except Exception as db_error:
                pass

        raise HTTPException(status_code=500, detail=f"Error creating digital twin: {str(e)}")

@app.get("/digital-twins", response_model=List[DigitalTwinResponse])
async def get_digital_twins(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    from database import DigitalTwin
    db_digital_twins = db.query(DigitalTwin).all()

    responses = []
    for db_digital_twin in db_digital_twins:
        response = {
            "id": str(db_digital_twin.id),
            "name": db_digital_twin.name,
            "youtube_url": db_digital_twin.youtube_url,
            "language": db_digital_twin.language,
            "collection_name": db_digital_twin.collection_name,
            "status": db_digital_twin.status,
            "created_at": db_digital_twin.created_at,
            "updated_at": db_digital_twin.updated_at
        }
        responses.append(DigitalTwinResponse(**response))

    return responses

@app.get("/digital-twins/{digital_twin_id}", response_model=DigitalTwinResponse)
async def get_digital_twin_by_id(
    digital_twin_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    db_digital_twin = db_get_digital_twin(db, digital_twin_id)
    if not db_digital_twin:
        raise HTTPException(status_code=404, detail="Digital twin not found")

    return db_digital_twin

@app.get("/digital-twins/{digital_twin_id}/status")
async def get_digital_twin_status(
    digital_twin_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    db_digital_twin = db_get_digital_twin(db, digital_twin_id)
    if not db_digital_twin:
        raise HTTPException(status_code=404, detail="Digital twin not found")

    return {"status": db_digital_twin.status}

@app.delete("/digital-twins/{digital_twin_id}")
async def delete_digital_twin(
    digital_twin_id: int,
    admin_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    db_digital_twin = db_get_digital_twin(db, digital_twin_id)
    if not db_digital_twin:
        raise HTTPException(status_code=404, detail="Digital twin not found")

    person_name = db_digital_twin.name
    collection_name = db_digital_twin.collection_name

    from digital_twin_utils import clean_digital_twin_data
    cleanup_success = clean_digital_twin_data(digital_twin_id, person_name, collection_name)

    db.delete(db_digital_twin)
    db.commit()

    if cleanup_success:
        return {
            "message": f"Digital twin {person_name} deleted successfully with all related data"
        }
    else:
        return {
            "message": f"Digital twin {person_name} deleted from database, but some related data cleanup failed. Check logs for details."
        }

@app.get("/documents/supported-formats")
async def get_supported_formats():
    """
    Desteklenen doküman formatlarının listesini döndür
    """
    formats = get_supported_file_extensions()
    
    format_categories = {
        "office": [f for f in formats if f in ['.docx', '.doc', '.xlsx', '.xls', '.pptx', '.ppt']],
        "pdf": [f for f in formats if f in ['.pdf']],
        "images": [f for f in formats if f in ['.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.gif']],
        "text": [f for f in formats if f in ['.txt', '.md', '.rst', '.rtf']],
        "web": [f for f in formats if f in ['.html', '.htm', '.xml']],
        "data": [f for f in formats if f in ['.json', '.csv', '.tsv']],
        "ebook": [f for f in formats if f in ['.epub']],
        "libreoffice": [f for f in formats if f in ['.odt', '.ods', '.odp']]
    }
    
    return {
        "total_supported": len(formats),
        "all_formats": formats,
        "categories": format_categories,
        "features": {
            "ocr_support": "Resim dosyalarından metin çıkarma (Türkçe ve İngilizce)",
            "pdf_advanced": "PyMuPDF ile gelişmiş PDF işleme + OCR fallback",
            "office_full": "Excel, Word, PowerPoint tam desteği",
            "web_parsing": "HTML/XML temizleyici parsing",
            "data_formats": "JSON, CSV structured parsing",
            "llamaindex": "LlamaIndex tabanlı gelişmiş doküman analizi"
        }
    }

@app.post("/documents", response_model=DocumentResponse)
async def upload_document(
    digital_twin_id_str: str = Form(..., alias="digital_twin_id"),
    file: UploadFile = File(...),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    admin_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    try:
        digital_twin_id = int(digital_twin_id_str)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid digital_twin_id format. Must be an integer.")

    db_digital_twin = db_get_digital_twin(db, digital_twin_id)
    if not db_digital_twin:
        raise HTTPException(status_code=404, detail=f"Digital twin with id {digital_twin_id} not found")

    person_name = db_digital_twin.name
    collection_name = db_digital_twin.collection_name

    file_content = await file.read()

    if not is_supported_file(file.filename):
        supported_formats = get_supported_file_extensions()
        supported_list = ", ".join(supported_formats)
        raise HTTPException(
            status_code=400, 
            detail=f"Desteklenmeyen dosya formatı. Desteklenen formatlar: {supported_list}"
        )

    global ocr_parser
    if ocr_parser:
        try:
            texts = ocr_parser.parse_document(file_content, file.filename)
            text_content = "\n\n".join(texts) if texts else None
        except Exception as e:
            print(f"Parser hatası: {e}")
            text_content = extract_text_from_any_document(file_content, file.filename)
    else:
        text_content = extract_text_from_any_document(file_content, file.filename)
    
    if not text_content:
        if file.filename.lower().endswith(".docx"):
            text_content = extract_text_from_docx(file_content)
        elif file.filename.lower().endswith(".pdf"):
            text_content = extract_text_from_pdf(file_content)

    if not text_content:
        raise HTTPException(status_code=400, detail="Could not extract text from file")

    db_document = create_document(db, digital_twin_id, file.filename, text_content)
    if not db_document:
        raise HTTPException(status_code=500, detail="Failed to create document in database")

    document_response_data = {
        "id": str(db_document.id),
        "digital_twin_id": str(digital_twin_id),
        "filename": db_document.filename,
        "status": db_document.status,
        "created_at": db_document.created_at
    }

    asyncio.create_task(
        process_document(
            person_name,
            text_content,
            collection_name,
            db_document.id
        )
    )

    return DocumentResponse(**document_response_data)

class MultipleDocumentsResponse(BaseModel):
    documents: List[DocumentResponse]
    total_uploaded: int
    total_failed: int
    failed_files: List[str]

@app.post("/documents/multiple", response_model=MultipleDocumentsResponse)
async def upload_multiple_documents(
    digital_twin_id_str: str = Form(..., alias="digital_twin_id"),
    files: List[UploadFile] = File(...),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    admin_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """
    Birden fazla dokümanı kabul et, veritabanına 'queued' olarak kaydet
    ve asıl işlemeyi arka plan görevlerine devret. Bu endpoint çok hızlı cevap verir.
    """
    try:
        digital_twin_id = int(digital_twin_id_str)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid digital_twin_id format. Must be an integer.")

    db_digital_twin = db_get_digital_twin(db, digital_twin_id)
    if not db_digital_twin:
        raise HTTPException(status_code=404, detail=f"Digital twin with id {digital_twin_id} not found")

    person_name = db_digital_twin.name
    collection_name = db_digital_twin.collection_name

    successful_documents = []
    failed_files = []
    
    for file in files:
        try:
            if not is_supported_file(file.filename):
                failed_files.append(file.filename)
                continue

            # Dosya içeriğini oku
            file_content = await file.read()

            # Dokümanı DB'de 'queued' olarak oluştur
            db_document = create_document(db, digital_twin_id, file.filename)
            if not db_document:
                failed_files.append(file.filename)
                continue

            # Arka plan görevi olarak asıl işlemeyi başlat
            background_tasks.add_task(
                process_document,
                document_id=db_document.id,
                file_content=file_content,
                filename=db_document.filename,
                person_name=person_name,
                collection_name=collection_name
            )

            document_response_data = {
                "id": str(db_document.id),
                "digital_twin_id": str(digital_twin_id),
                "filename": db_document.filename,
                "status": db_document.status, # 'queued'
                "created_at": db_document.created_at
            }
            successful_documents.append(DocumentResponse(**document_response_data))

        except Exception as e:
            # Bu dosya için bir hata oluştu, logla ve devam et
            print(f"Dosya '{file.filename}' yüklenirken hata oluştu: {e}")
            failed_files.append(file.filename)

    return MultipleDocumentsResponse(
        documents=successful_documents,
        total_uploaded=len(successful_documents),
        total_failed=len(failed_files),
        failed_files=failed_files
    )

@app.get("/documents/{document_id}/status")
async def get_document_status(
    document_id: str,
    db: Session = Depends(get_db)
):
    try:
        doc_id = int(document_id)
        
        db_document = db_get_document(db, doc_id)
        if not db_document:
            raise HTTPException(status_code=404, detail="Document not found")
            
        return {
            "id": str(db_document.id),
            "filename": db_document.filename,
            "status": db_document.status,
            "progress": getattr(db_document, 'progress', 0)
        }
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid document ID format")

class MultipleDocumentsStatusResponse(BaseModel):
    document_statuses: List[Dict[str, Any]]
    total_processing: int
    total_completed: int
    total_failed: int
    overall_progress: int
    is_all_done: bool

@app.post("/documents/status/multiple", response_model=Dict[str, Any])
def get_multiple_documents_status(
    document_ids: List[str] = Body(...), 
):
    """
    Birden fazla dokümanın durumunu ve işleme ilerlemesini daha dayanıklı bir şekilde kontrol et.
    Her denemede yeni bir veritabanı oturumu kullanarak anlık bağlantı hatalarını tolere eder.
    """
    max_retries = 5
    retry_delay = 3

    for attempt in range(max_retries):
        try:
            # Her deneme için taze bir veritabanı oturumu (session) kullan
            with get_db_session() as db:
                document_statuses = []
                all_done = True
                
                from digital_twin_db import get_documents_by_ids
                documents_in_db = get_documents_by_ids(db, [int(doc_id) for doc_id in document_ids if doc_id.isdigit()])
                doc_map = {str(doc.id): doc for doc in documents_in_db}

                for doc_id_str in document_ids:
                    db_document = doc_map.get(doc_id_str)
                    
                    if db_document:
                        status = db_document.status
                        progress = db_document.progress
                        if status not in ["completed", "failed"]:
                            all_done = False
                    else:
                        status = "processing"
                        progress = 0
                        all_done = False

                    document_statuses.append({
                        "id": doc_id_str,
                        "status": status,
                        "progress": progress
                    })
                
                return {
                    "document_statuses": document_statuses,
                    "is_all_done": all_done
                }

        except OperationalError as e:
            print(f"Veritabanı operasyon hatası (Deneme {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue # Bir sonraki denemede yeni bir session ile tekrar denenecek
            else:
                print("Veritabanına ulaşılamıyor, maksimum deneme sayısına ulaşıldı.")
                raise HTTPException(
                    status_code=503, 
                    detail="Veritabanı geçici olarak yoğun veya ulaşılamaz durumda. Lütfen birkaç saniye sonra tekrar deneyin."
                )
        
        except Exception as e:
            print(f"Doküman durumları alınırken beklenmedik hata (Deneme {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
            else:
                raise HTTPException(status_code=500, detail=f"Doküman durumları alınırken kritik bir hata oluştu: {e}")

@app.delete("/documents/{document_id}")
async def delete_document(
    document_id: str,
    admin_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    try:
        doc_id = int(document_id)
        
        db_document = db_get_document(db, doc_id)
        if not db_document:
            raise HTTPException(status_code=404, detail="Document not found")

        db_digital_twin = db_get_digital_twin(db, db_document.digital_twin_id)
        if not db_digital_twin:
            raise HTTPException(status_code=404, detail="Associated digital twin not found")

        collection_name = db_digital_twin.collection_name
        qdrant_deleted = delete_document_from_collection(collection_name, doc_id)

        db_delete_document(db, doc_id)

        msg = f"Document {db_document.filename} deleted successfully"
        if not qdrant_deleted:
            msg += ", but some vectors could not be removed from Qdrant"

        return {"message": msg}
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid document ID format")

@app.get("/documents", response_model=List[DocumentResponse])
async def get_documents(
    digital_twin_id: str,
    admin_user: User = Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    try:
        dt_id = int(digital_twin_id)
        
        db_digital_twin = db_get_digital_twin(db, dt_id)
        if not db_digital_twin:
            raise HTTPException(status_code=404, detail="Digital twin not found")
            
        db_documents = db_get_documents_by_digital_twin(db, dt_id)
        
        documents = []
        for db_document in db_documents:
            document_data = {
                "id": str(db_document.id),
                "digital_twin_id": str(db_document.digital_twin_id),
                "filename": db_document.filename,
                "status": db_document.status,
                "created_at": db_document.created_at
            }
            documents.append(DocumentResponse(**document_data))
            
        return documents
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid digital_twin_id format")

class ChatCreate(BaseModel):
    digital_twin_id: str

@app.post("/chats")
async def create_chat(
    chat_data: ChatCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    try:
        digital_twin_id = int(chat_data.digital_twin_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid digital_twin_id format")

    db_digital_twin = db_get_digital_twin(db, digital_twin_id)
    if not db_digital_twin:
        raise HTTPException(status_code=404, detail="Digital twin not found")

    sanitized_name = sanitize_name(db_digital_twin.name)
    chat_id = f"chat_{sanitized_name}_{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}"

    from digital_twin_utils import Memory as DTMemory
    chat_memories.setdefault(chat_id, DTMemory())
    conversation_histories.setdefault(chat_id, [])

    memory_key = f"dt_{digital_twin_id}"
    chat_memories.setdefault(memory_key, DTMemory())
    conversation_histories.setdefault(memory_key, [])

    return {"id": chat_id, "digital_twin_id": str(digital_twin_id), "created_at": datetime.now(timezone.utc)}

@app.post("/chats/{chat_id}/messages")
async def send_message(
    chat_id: str,
    message: ChatMessage,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    try:
        digital_twin_id = int(chat_id)
    except ValueError:
        if chat_id not in chat_memories:
            raise HTTPException(status_code=404, detail="Chat not found")
        digital_twin_id = 1

    db_digital_twin = db_get_digital_twin(db, digital_twin_id)
    if not db_digital_twin:
        raise HTTPException(status_code=404, detail="Digital twin not found")

    collection_name = db_digital_twin.collection_name

    memory_key = f"dt_{digital_twin_id}"

    if memory_key not in chat_memories:
        chat_memories[memory_key] = Memory()
    if memory_key not in conversation_histories:
        conversation_histories[memory_key] = []

    personality_str = db_digital_twin.personality
    if not personality_str:
        raise HTTPException(status_code=500, detail=f"No personality found for {db_digital_twin.name}")

    try:
        personality = json.loads(personality_str)
    except json.JSONDecodeError as e:
        raise HTTPException(status_code=500, detail=f"Error decoding personality JSON: {str(e)}")

    bot_response = chat_with_digital_twin(
        user_input=message.content,
        person_name=db_digital_twin.name,
        collection_name=collection_name,
        personality=personality,
        memory=chat_memories[memory_key],
        conversation_history=conversation_histories[memory_key]
    )

    return {
        "user_message_id": f"user_{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}",
        "bot_message_id": f"bot_{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}",
        "bot_response": bot_response
    }

@app.get("/chats", response_model=List[ChatResponse])
async def get_chats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    db_digital_twins = db_get_digital_twins(db)

    chat_responses = []
    for dt in db_digital_twins:
        name = dt.name.lower().replace(' ', '_')
        chat_responses.append({
            "id": f"chat_{name}_{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}",
            "digital_twin_id": str(dt.id),
            "created_at": datetime.now(timezone.utc)
        })

    return [ChatResponse(**chat) for chat in chat_responses]

@app.get("/chats/{chat_id}/messages", response_model=List[MessageResponse])
async def get_messages(
    chat_id: str,
    current_user: User = Depends(get_current_active_user)
):
    return []

async def process_digital_twin(
    digital_twin_id: int,
    display_name: str,
    youtube_url: str,
    collection_name: str
):
    try:
        with get_db_session() as db:
            update_digital_twin_status(db, digital_twin_id, "processing")

            transcript = get_transcript(youtube_url)
            if not transcript:
                update_digital_twin_status(db, digital_twin_id, "failed")
                return

            personality = analyze_personality(transcript, display_name)
            if not personality:
                update_digital_twin_status(db, digital_twin_id, "failed")
                return

            personality["display_name"] = display_name

            update_digital_twin_personality(db, digital_twin_id, personality)

            create_collection(collection_name)

            update_digital_twin_status(db, digital_twin_id, "completed")

    except Exception as e:
        try:
            with get_db_session() as db:
                update_digital_twin_status(db, digital_twin_id, "failed")
        except Exception as inner_e:
            pass

def process_document(
    document_id: int,
    file_content: bytes,
    filename: str,
    person_name: str,
    collection_name: str
):
    """
    Arka plan görevi: Bir dokümanı A'dan Z'ye işler.
    1. Durumu 'parsing' olarak günceller.
    2. Dosya içeriğini LlamaParse ile işler.
    3. Ayrıştırılmış metni ve durumu 'processing' olarak DB'ye kaydeder.
    4. Metni parçalara ayırır (chunking).
    5. Parçaları Qdrant'a gömer (embedding).
    6. Nihai durumu ('completed' veya 'failed') DB'ye kaydeder.
    """
    from digital_twin_db import (
        update_document_status, 
        update_document_content_and_status,
        update_document_progress, 
        update_document_status_and_progress
    )

    # 1. Parsing Aşaması
    try:
        with get_db_session() as db:
            update_document_status(db, document_id, "parsing")

        global ocr_parser
        if not ocr_parser:
             raise Exception("OCR Parser başlatılmamış.")

        texts = ocr_parser.parse_document(file_content, filename)
        text_content = "\n\n".join(texts) if texts else ""

        if not text_content or not text_content.strip():
            with get_db_session() as db:
                update_document_status_and_progress(db, document_id, "failed", 0)
            print(f"Doküman (id:{document_id}) için içerik çıkarılamadı.")
            return

        with get_db_session() as db:
            # Ayrıştırılmış metni kaydet ve durumu 'processing' yap
            update_document_content_and_status(db, document_id, text_content, "processing")
            update_document_progress(db, document_id, 10)

    except Exception as e:
        print(f"❌ Parsing hatası (document_id: {document_id}): {e}")
        with get_db_session() as db:
            update_document_status_and_progress(db, document_id, "failed", 0)
        return

    # 2. Embedding Aşaması
    try:
        create_collection(collection_name)
        
        cleaned_text = clean_text(text_content) # text_content artık DB'den değil, parse işleminden geliyor
        chunks = split_text_into_chunks(cleaned_text, chunk_size=1000)

        if not chunks:
            with get_db_session() as db:
                update_document_status_and_progress(db, document_id, "failed", 0)
            return

        with get_db_session() as db:
            update_document_progress(db, document_id, 40)

        successful_chunks = 0
        total_chunks = len(chunks)
        
        for i, chunk in enumerate(chunks):
            current_progress = 40 + int((i / total_chunks) * 50)
            with get_db_session() as db:
                update_document_progress(db, document_id, current_progress)
            
            try:
                metadata = {
                    "source": "document", "chunk_index": i, "total_chunks": total_chunks,
                    "document_id": document_id, "person_name": person_name,
                    "timestamp": str(datetime.now(timezone.utc))
                }
                success = add_to_collection(collection_name, chunk, metadata)
                if success:
                    successful_chunks += 1
            except Exception as chunk_error:
                print(f"❌ Chunk işleme hatası (document_id: {document_id}, chunk: {i}): {chunk_error}")
        
        success_rate = successful_chunks / total_chunks if total_chunks > 0 else 0
        
        with get_db_session() as db:
            if success_rate >= 0.8:
                update_document_status_and_progress(db, document_id, "completed", 100)
            else:
                update_document_status_and_progress(db, document_id, "failed", int(success_rate * 100))
                print(f"Doküman (id:{document_id}) düşük başarı oranıyla tamamlandı: {success_rate:.2f}")

    except Exception as e:
        print(f"❌ Embedding hatası (document_id: {document_id}): {e}")
        with get_db_session() as db:
            update_document_status_and_progress(db, document_id, "failed", 0)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000,
        timeout_keep_alive=86400,
        timeout_graceful_shutdown=86400,
    )





