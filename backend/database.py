from sqlalchemy import create_engine, <PERSON><PERSON>n, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, DateTime, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
import os
from datetime import datetime, timezone
from dotenv import load_dotenv
import time
from sqlalchemy.exc import OperationalError

load_dotenv()

def get_db_url():
    """Çevresel değişkenlerden veritabanı URL'ini al."""
    return os.getenv("DATABASE_URL", "********************************************/digital_twin_db")

DATABASE_URL = get_db_url()

def create_db_engine_with_retry(retries=5, delay=5):
    """
    Veritabanı motorunu yeniden deneme mantığıyla oluşturur.
    Veritabanı başlangıçta hazır değilse birkaç kez dener.
    """
    for i in range(retries):
        try:
            engine = create_engine(
                DATABASE_URL,
                connect_args={
                    "client_encoding": "utf8",
                    "connect_timeout": 30,
                    "application_name": "digital_twin_backend"
                },
                pool_pre_ping=True,
                pool_size=10,
                max_overflow=20,
                pool_timeout=30,
                pool_recycle=1800,
                echo=False
            )
            # Test bağlantısı kur
            with engine.connect() as connection:
                print("✅ Veritabanı motoru başarıyla oluşturuldu ve bağlantı test edildi.")
                return engine
        except OperationalError as e:
            print(f"❌ Veritabanı motoru oluşturulamadı. Hata: {e}")
            if i < retries - 1:
                print(f"Tekrar denenecek... ({i+1}/{retries}). {delay} saniye bekleniyor.")
                time.sleep(delay)
            else:
                print("❌ Maksimum deneme sayısına ulaşıldı. Veritabanı motoru oluşturulamadı.")
                raise

# SQLAlchemy engine ve session konfigürasyonu
engine = create_db_engine_with_retry()
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    """FastAPI dependency için veritabanı session'ı."""
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        print(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        try:
            db.close()
        except Exception as e:
            print(f"Error closing database session: {e}")

from contextlib import contextmanager
from sqlalchemy import text

@contextmanager
def get_db_session():
    """Arkaplan görevleri için veritabanı session context manager'ı."""
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        print(f"Database session error in context manager: {e}")
        try:
            db.rollback()
        except Exception as rollback_error:
            print(f"Error during rollback: {rollback_error}")
        raise e
    finally:
        try:
            db.close()
        except Exception as close_error:
            print(f"Error closing database session in context manager: {close_error}")

def check_db_health():
    """Veritabanı sağlık durumunu kontrol et."""
    try:
        with get_db_session() as db:
            result = db.execute(text("SELECT 1")).fetchone()
            return True, "Database is healthy"
    except Exception as e:
        return False, f"Database health check failed: {e}"

# Veritabanı modelleri
class User(Base):
    """Kullanıcı modeli."""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    email = Column(String, unique=True, index=True, nullable=True)
    hashed_password = Column(String, nullable=False)
    role = Column(String, default="user")
    is_admin = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # İlişkiler
    digital_twins = relationship("DigitalTwin", back_populates="user")
    chats = relationship("Chat", back_populates="user")

class DigitalTwin(Base):
    """Dijital ikiz modeli."""
    __tablename__ = "digital_twins"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    name = Column(String, index=True)
    sanitized_name = Column(String, unique=True, index=True)
    collection_name = Column(String, unique=True)
    youtube_url = Column(String, nullable=True)
    language = Column(String, default="tr")
    transcript = Column(Text, nullable=True)
    personality = Column(Text, nullable=True)  # JSON string
    status = Column(String, default="processing")
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # İlişkiler
    user = relationship("User", back_populates="digital_twins")
    chats = relationship("Chat", back_populates="digital_twin")
    documents = relationship("Document", back_populates="digital_twin")

class Document(Base):
    """Doküman modeli."""
    __tablename__ = "documents"

    id = Column(Integer, primary_key=True, index=True)
    digital_twin_id = Column(Integer, ForeignKey("digital_twins.id"))
    filename = Column(String)
    content = Column(Text)
    status = Column(String, default="processing")
    progress = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # İlişkiler
    digital_twin = relationship("DigitalTwin", back_populates="documents")

class Chat(Base):
    """Sohbet modeli."""
    __tablename__ = "chats"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    digital_twin_id = Column(Integer, ForeignKey("digital_twins.id"))
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # İlişkiler
    user = relationship("User", back_populates="chats")
    digital_twin = relationship("DigitalTwin", back_populates="chats")
    messages = relationship("Message", back_populates="chat")

class Message(Base):
    """Mesaj modeli."""
    __tablename__ = "messages"

    id = Column(Integer, primary_key=True, index=True)
    chat_id = Column(Integer, ForeignKey("chats.id"))
    is_user = Column(Boolean, default=True)  # True: kullanıcı, False: dijital ikiz
    content = Column(Text)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))

    # İlişkiler
    chat = relationship("Chat", back_populates="messages")

def create_tables():
    """Tüm tabloları oluştur."""
    Base.metadata.create_all(bind=engine)
