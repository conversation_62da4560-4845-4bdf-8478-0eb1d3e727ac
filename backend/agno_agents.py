import os
import json
import re
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
import requests
from bs4 import BeautifulSoup
from agno.agent import Agent
from agno.tools import tool
from agno.vectordb.qdrant import Qdrant
from agno.embedder.ollama import <PERSON>llamaEmbedder
from agno.embedder.base import Embedder
from agno.models.openai import OpenAIChat
from agno.storage.sqlite import SqliteStorage
from agno.memory.v2.db.sqlite import SqliteMemoryDb
from agno.memory.v2.memory import Memory
from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct, VectorParams, Distance

load_dotenv("/app/.env")

def ensure_data_directory():
    """
    /app/data klasörünün var olduğundan emin olur.
    """
    data_dir = "/app/data"
    if not os.path.exists(data_dir):
        os.makedirs(data_dir, exist_ok=True)
        print(f"Created data directory: {data_dir}")
    else:
        print(f"Data directory already exists: {data_dir}")

QDRANT_URL = os.getenv("QDRANT_URL")
OLLAMA_URL_FROM_ENV = os.getenv("OLLAMA_URL")
OLLAMA_MODEL_FROM_ENV = os.getenv("OLLAMA_EMBEDDER_MODEL") 
EMBEDDER_DIMENSIONS = int(os.getenv("EMBEDDER_DIMENSIONS", "768"))

if OLLAMA_URL_FROM_ENV:
    os.environ["OLLAMA_URL"] = OLLAMA_URL_FROM_ENV 
    os.environ["OLLAMA_HOST"] = OLLAMA_URL_FROM_ENV 

if OLLAMA_MODEL_FROM_ENV:
    os.environ["OLLAMA_EMBEDDER_MODEL"] = OLLAMA_MODEL_FROM_ENV 
    os.environ["OLLAMA_MODEL"] = OLLAMA_MODEL_FROM_ENV 

print(f"DEBUG: Effective OLLAMA_URL in os.environ: {os.getenv('OLLAMA_URL')}")
print(f"DEBUG: Effective OLLAMA_HOST in os.environ: {os.getenv('OLLAMA_HOST')}")
print(f"DEBUG: Effective OLLAMA_EMBEDDER_MODEL in os.environ: {os.getenv('OLLAMA_EMBEDDER_MODEL')}")
print(f"DEBUG: Effective OLLAMA_MODEL in os.environ: {os.getenv('OLLAMA_MODEL')}")
print(f"DEBUG: Loaded QDRANT_URL from .env: {QDRANT_URL}")
print(f"DEBUG: Loaded EMBEDDER_DIMENSIONS from .env: {EMBEDDER_DIMENSIONS}")

OLLAMA_URL = os.getenv("OLLAMA_URL")
OLLAMA_EMBEDDER_MODEL = os.getenv("OLLAMA_EMBEDDER_MODEL")

class CustomOllamaEmbedder(Embedder):
    """
    Agno Embedder arayüzünü uygulayan özel bir Ollama embedder.
    Bu, agno kütüphanesinin ollamaEmbedder'ına güvenmek yerine kendi Ollama
    API çağrımızı yapıp model adını doğrudan kontrol etmemizi sağlar.
    """

    def __init__(self, model_name: str = None, ollama_url: str = None):
        """
        CustomOllamaEmbedder sınıfını başlatır.

        Args:
            model_name: Kullanılacak Ollama model adı (None ise çevresel değişkenlerden okur)
            ollama_url: Ollama API URL'si (None ise çevresel değişkenlerden okur)
        """
        super().__init__()
        self.model = model_name or OLLAMA_EMBEDDER_MODEL or "nomic-embed-text"
        self.url = ollama_url or OLLAMA_URL or "http://localhost:11434"

        print(f"CustomOllamaEmbedder initialized with model: {self.model}")
        print(f"CustomOllamaEmbedder initialized with URL: {self.url}")

    def embed_query(self, text: str) -> List[float]:
        """
        Verilen metni vektöre dönüştürür. Bu, Agno Embedder arayüzünden gelen bir yöntemdir.

        Args:
            text: Vektöre dönüştürülecek metin

        Returns:
            Vektör (float listesi)
        """
        return self.get_embedding(text)

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Bir dizi metni vektöre dönüştürür. Bu, Agno Embedder arayüzünden gelen bir yöntemdir.

        Args:
            texts: Vektöre dönüştürülecek metin listesi

        Returns:
            Vektör listesi (float listelerinin listesi)
        """
        return [self.get_embedding(text) for text in texts]

    def get_embedding(self, text: str) -> List[float]:
        """
        Ollama API'sini kullanarak metin için embedding alır.

        Args:
            text: Vektöre dönüştürülecek metin

        Returns:
            Vektör (float listesi)
        """
        print(f"CustomOllamaEmbedder.get_embedding called with text: '{text[:50]}...' (truncated)")
        print(f"Using model: {self.model}, URL: {self.url}")

        try:
            response = requests.post(
                f"{self.url}/api/embeddings",
                json={"model": self.model, "prompt": text}
            )
            response.raise_for_status()

            result = response.json()
            embedding = result.get("embedding", [])

            if embedding:
                print(f"Successfully got embedding from Ollama with dimension {len(embedding)}")

                if len(embedding) != EMBEDDER_DIMENSIONS:
                    print(f"WARNING: Embedding dimension mismatch. Expected {EMBEDDER_DIMENSIONS}, got {len(embedding)}")
                    if len(embedding) < EMBEDDER_DIMENSIONS:
                        embedding.extend([0.0] * (EMBEDDER_DIMENSIONS - len(embedding)))
                    else:
                        embedding = embedding[:EMBEDDER_DIMENSIONS]

                return embedding
            else:
                raise ValueError("Empty embedding returned from Ollama")

        except Exception as e:
            print(f"ERROR in CustomOllamaEmbedder.get_embedding: {str(e)}")

            print("Error in embedding, returning empty list")
            raise ValueError(f"Failed to get embedding from Ollama: {str(e)}")

with open("/app/.env", "r") as env_file:
    for line in env_file:
        if line.startswith("OPENROUTER_API_KEY="):
            OPENROUTER_API_KEY = line.strip().split("=", 1)[1]
        elif line.startswith("OPENROUTER_URL="):
            OPENROUTER_URL_FROM_ENV_FILE = line.strip().split("=", 1)[1] 
        elif line.startswith("OPENROUTER_MODEL="):
            OPENROUTER_MODEL_FROM_ENV_FILE = line.strip().split("=", 1)[1] 

OPENROUTER_URL = OPENROUTER_URL_FROM_ENV_FILE if 'OPENROUTER_URL_FROM_ENV_FILE' in locals() and OPENROUTER_URL_FROM_ENV_FILE else "https://openrouter.ai/api/v1"
OPENROUTER_MODEL = OPENROUTER_MODEL_FROM_ENV_FILE if 'OPENROUTER_MODEL_FROM_ENV_FILE' in locals() and OPENROUTER_MODEL_FROM_ENV_FILE else "google/gemini-2.0-flash-001"

if not OPENROUTER_API_KEY:
    print("WARNING: OpenRouter API key is not set!")
else:
    print(f"OpenRouter API key loaded: {OPENROUTER_API_KEY[:10]}...")

if not OPENROUTER_URL:
    print("WARNING: OpenRouter URL is not set!")
else:
    print(f"OpenRouter URL loaded: {OPENROUTER_URL}")

if not OPENROUTER_MODEL:
    print("WARNING: OpenRouter model is not set!")
else:
    print(f"OpenRouter model loaded: {OPENROUTER_MODEL}")

os.environ["OPENAI_API_KEY"] = OPENROUTER_API_KEY
os.environ["OPENAI_API_BASE"] = OPENROUTER_URL


class PersonalityAnalysisAgent:
    """
    YouTube videosundan kişilik profili çıkaran agent.
    """

    def __init__(self):
        """
        PersonalityAnalysisAgent sınıfını başlatır.
        """
        print(f"PersonalityAnalysisAgent initialized without memory - memory only used in DigitalTwinAgent")
        
        self.agent = self._create_agent()

    def _create_agent(self) -> Agent:
        """
        Kişilik analizi ajanını oluşturur.

        Returns:
            Oluşturulan ajan
        """
        instructions = [
            "Sen bir kişilik analizi uzmanısın.",
            "Amacın, verilen YouTube video transkriptini analiz ederek kişinin konuşma tarzı, düşünce yapısı ve kişilik özellikleri hakkında detaylı bir profil çıkarmak.",
            "Analiz sonucunu aşağıdaki JSON formatında döndürmelisin:",
            """
            {
                "konusma_tarzi": {
                    "genel_ton": "",
                    "cumle_yapisi": "",
                    "kelime_secimi": "",
                    "dil_ozellikleri": ["", "", ""]
                },
                "sik_kullanilan_tonlama_kelimeleri": [
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    ""
                ],
                "bilgi_alanlari": [
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",

                ],
                "dusunce_yapisi": {
                    "yaklasim": "",
                    "analiz_sekli": "",
                    "karar_verme": ""
                },
                "kisilik_ozellikleri": [
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",
                    "",

                ]
            }
            """,
            "Transkripti dikkatlice ve çok detaylı bir şekilde analiz et ve kişinin gerçek konuşma tarzını, sık kullandığı tonlama kelimelerini, bilgi alanlarını, düşünce yapısını ve kişilik özelliklerini belirle.",
            "Analiz sonucunu JSON formatında döndür, başka açıklama ekleme."
        ]

        print(f"Using OpenRouter model: {OPENROUTER_MODEL}")
        print(f"Using API key: {OPENROUTER_API_KEY[:10]}...")
        print(f"Using base URL: {OPENROUTER_URL}")

        model = OpenAIChat(
            id=OPENROUTER_MODEL,
            api_key=OPENROUTER_API_KEY,
            base_url=OPENROUTER_URL,
            default_headers={"HTTP-Referer": "https://example.com"}
        )

        agent = Agent(
            name="PersonalityAnalysisAgent",
            instructions=instructions,
            model=model,
            description="Kişilik analizi yapan uzman ajan",
            show_tool_calls=False,
            markdown=True,
            debug_mode=True
        )

        return agent

    def analyze_personality(self, transcript_text: str, person_name: str) -> Dict[str, Any]:
        """
        YouTube video transkriptini analiz ederek kişilik profili çıkarır.
        """
        prompt = f"""
        Aşağıdaki metin, {person_name} adlı kişinin bir YouTube videosunun transkriptidir.
        Bu metni analiz ederek aşağıdaki yapıda detaylı bir kişilik profili oluştur:

        ### İstenen JSON Formatı:
        {{
            "konusma_tarzi": {{
                "genel_ton": "Örnek: Samimi, resmi, enerjik...",
                "cumle_yapisi": "Örnek: Kısa cümleler, karmaşık yapılar...",
                "kelime_secimi": "Örnek: Teknik terimler, günlük dil...",
                "dil_ozellikleri": ["Örnek: Argo kullanımı", "Metaforlar"]
            }},
            "sik_kullanilan_tonlama_kelimeleri": [
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                "",
                ""
            ],
            "bilgi_alanlari": ["Teknoloji", "Psikoloji", "Tarih"],
            "dusunce_yapisi": {{
                "yaklasim": "Örnek: Analitik, duygusal...",
                "analiz_sekli": "Örnek: Veri odaklı, sezgisel...",
                "karar_verme": "Örnek: Hızlı karar verir, detayları tartışır..."
            }},
            "kisilik_ozellikleri": ["Dışa dönük", "Empatik"]
        }}

        ### Transkript:
        {transcript_text}


        Olabildiğince çok ama çok detaylı bir şekilde analiz et ve çıkarım yap.
        Lütfen yukarıdaki yapıya tam olarak uygun bir JSON döndür.
        [isim] yerine {person_name} kullan.
        """

        raw_agent_response = self.agent.run(prompt)

        processed_response_text = ""
        if isinstance(raw_agent_response, str):
            processed_response_text = raw_agent_response
        elif hasattr(raw_agent_response, 'content'): 
            processed_response_text = raw_agent_response.content
        else:
            raise ValueError(f"Unable to extract string content from agent response of type {type(raw_agent_response)}")

        if "```json" in processed_response_text:
            try:
                json_str = processed_response_text.split("```json")[1].split("```")[0].strip()
            except IndexError:
                potential_json_part = processed_response_text.split("```json", 1)[-1]
                json_str = potential_json_part.split("```", 1)[0].strip() if "```" in potential_json_part else potential_json_part.strip()
        else:
            json_str = processed_response_text.strip()

        try:
            personality = json.loads(json_str)
        except json.JSONDecodeError as e:
            print(f"JSON Decode Error: {e}")
            print(f"Problematic JSON string: '{json_str}'")
            raise ValueError(f"Failed to parse personality JSON from agent response. Content: {json_str}") from e

        for i, ifade in enumerate(personality.get("sik_kullanilan_tonlama_kelimeleri", [])):
            personality["sik_kullanilan_tonlama_kelimeleri"][i] = ifade.replace("[isim]", person_name)

        return personality


def extract_and_embed_text_from_youtube(text: str, person_name: str, collection_name: str) -> bool:
    """
    YouTube video transkriptini parçalara böler ve Qdrant koleksiyonuna ekler.

    Args:
        text: YouTube video transkripti
        person_name: Kişinin adı
        collection_name: Qdrant koleksiyon adı

    Returns:
        bool: İşlem başarılı ise True, değilse False
    """
    try:
        print(f"extract_and_embed_text_from_youtube called for {person_name}")
        print(f"Text length: {len(text)} characters")
        print(f"Collection name: {collection_name}")

        from digital_twin_utils import (
            split_text_into_chunks,
            add_to_collection,
            create_collection,
            check_collection_exists,
            clean_text
        )

        cleaned_text = clean_text(text)
        if not cleaned_text:
            print("No text to process after cleaning")
            return False

        print(f"Cleaned text length: {len(cleaned_text)} characters")

        if not check_collection_exists(collection_name):
            print(f"Creating collection: {collection_name}")
            create_collection(collection_name)
        else:
            print(f"Collection '{collection_name}' exists.")

        chunks = split_text_into_chunks(cleaned_text, chunk_size=1000)
        print(f"Split text into {len(chunks)} chunks")

        success_count = 0
        for i, chunk in enumerate(chunks):
            try:
                metadata = {
                    "person_name": person_name,
                    "chunk_index": i,
                    "total_chunks": len(chunks),
                    "source": "youtube_transcript",
                    "timestamp": str(datetime.now())
                }

                success = add_to_collection(collection_name, chunk, metadata)
                if success:
                    success_count += 1
                    print(f"Successfully added chunk {i+1}/{len(chunks)} to collection")
                else:
                    print(f"Failed to add chunk {i+1}/{len(chunks)} to collection")

            except Exception as e:
                print(f"Error adding chunk {i+1} to collection: {str(e)}")

        print(f"Successfully added {success_count}/{len(chunks)} chunks to collection '{collection_name}'")

        return success_count >= len(chunks) / 2

    except Exception as e:
        print(f"Error in extract_and_embed_text_from_youtube: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


class DigitalTwinAgent:
    """
    Dijital ikiz ajanı sınıfı (Agno kütüphanesi kullanarak).
    """

    def __init__(self, person_name: str, personality: Dict[str, Any], conversation_history=None):
        """
        DigitalTwinAgent sınıfını başlatır.

        Args:
            person_name: Dijital ikizi oluşturulan kişinin adı
            personality: Kişilik analizi sözlüğü (PostgreSQL'den alınır)
            conversation_history: Önceki konuşma geçmişi
        """
        self.person_name = person_name
        self.collection_name = self._sanitize_name(person_name) + "_collection"
        self.personality = personality
        self.conversation_history = conversation_history or []

        print(f"DigitalTwinAgent initialized for {person_name} with personality data from PostgreSQL")
        ensure_data_directory()
        safe_name = self._sanitize_name(person_name)
        
        self.memory_db = SqliteMemoryDb(
            table_name=f"{safe_name}_memory",
            db_file=f"/app/data/{safe_name}_memory.db"
        )
        
        self.memory = Memory(
            db=self.memory_db
        )
        
        self.storage = SqliteStorage(
            table_name=f"{safe_name}_sessions",
            db_file=f"/app/data/{safe_name}_storage.db"
        )
        
        print(f"DigitalTwinAgent memory initialized for {person_name} with db: /app/data/{safe_name}_memory.db")
        print(f"DigitalTwinAgent storage initialized for {person_name} with db: /app/data/{safe_name}_storage.db")

        self._ensure_ollama_model_loaded()

        self.embedder = CustomOllamaEmbedder(
            model_name=OLLAMA_EMBEDDER_MODEL,
            ollama_url=OLLAMA_URL
        )

        self._ensure_qdrant_collection_exists()

        self.vector_db = Qdrant(
            collection=self.collection_name,
            url=QDRANT_URL,
            embedder=self.embedder
        )

        self.agent = self._create_agent()

    def _sanitize_name(self, name: str) -> str:
        """
        İsmi koleksiyon adı olarak kullanmak için temizler.

        Args:
            name: Temizlenecek isim

        Returns:
            Temizlenmiş isim
        """
        safe_name = name.lower().replace(' ', '_')
        return safe_name

    def _ensure_ollama_model_loaded(self):
        """
        Ollama modelinin yüklü olduğundan emin olur.
        """
        import requests
        import time

        model_name = OLLAMA_EMBEDDER_MODEL
        ollama_url = OLLAMA_URL

        print(f"[DEBUG] agno_agents.py: Ensuring Ollama model '{model_name}' is loaded")

        try:
            response = requests.get(f"{ollama_url}/api/tags")
            if response.status_code == 200:
                models = response.json().get("models", [])
                model_names = [model.get("name") for model in models]

                if model_name in model_names:
                    print(f"[DEBUG] agno_agents.py: Ollama model '{model_name}' is already loaded")
                    return

            print(f"[DEBUG] agno_agents.py: Ollama model '{model_name}' is not loaded, pulling it now")
            pull_response = requests.post(
                f"{ollama_url}/api/pull",
                json={"name": model_name}
            )

            if pull_response.status_code == 200:
                print(f"[DEBUG] agno_agents.py: Successfully pulled Ollama model '{model_name}'")
            else:
                print(f"[ERROR] agno_agents.py: Failed to pull Ollama model '{model_name}': {pull_response.text}")

            time.sleep(5)

        except Exception as e:
            print(f"[ERROR] agno_agents.py: Error ensuring Ollama model is loaded: {str(e)}")
            print("[WARNING] agno_agents.py: Continuing without ensuring model is loaded")

    def _ensure_qdrant_collection_exists(self):
        """
        Qdrant koleksiyonunun var olduğundan emin olur.
        """
        from qdrant_client import QdrantClient
        from qdrant_client.models import VectorParams, Distance

        qdrant_url = QDRANT_URL
        collection_name = self.collection_name
        vector_size = EMBEDDER_DIMENSIONS

        print(f"[DEBUG] agno_agents.py: Ensuring Qdrant collection '{collection_name}' exists")

        try:
            qdrant_client = QdrantClient(url=qdrant_url)

            collections = qdrant_client.get_collections().collections
            collection_names = [collection.name for collection in collections]

            if collection_name in collection_names:
                print(f"[DEBUG] agno_agents.py: Qdrant collection '{collection_name}' already exists")
                return

            print(f"[DEBUG] agno_agents.py: Creating Qdrant collection '{collection_name}'")
            qdrant_client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(size=vector_size, distance=Distance.COSINE)
            )

            print(f"[DEBUG] agno_agents.py: Successfully created Qdrant collection '{collection_name}'")

        except Exception as e:
            print(f"[ERROR] agno_agents.py: Error ensuring Qdrant collection exists: {str(e)}")
            print("[WARNING] agno_agents.py: Continuing without ensuring collection exists")

    def _create_agent(self) -> Agent:
        """
        Dijital ikiz ajanını oluşturur.

        Returns:
            Oluşturulan ajan
        """
        outer_self = self

        @tool(
            name="search_knowledge_base",
            description="Bilgi tabanında arama yapar - HER SORUDA MUTLAKA İLK OLARAK KULLANILMALIDIR. Bu araç olmadan cevap verme!",
            show_result=False
        )
        def search_knowledge_base_tool(query: str, limit: int = 5) -> List[Dict[str, Any]]:
            """
            Bilgi tabanında arama yapar. HER SORUDA MUTLAKA İLK OLARAK KULLANILMALIDIR. Bu araç olmadan cevap verme!

            ÇOK ÖNEMLİ: Bu araç, kullanıcının sorusuna cevap vermek için gerekli bilgileri sağlar.
            Bu aracı kullanmadan cevap verirsen, cevabın eksik ve hatalı olacaktır.

            Args:
                query: Arama sorgusu
                limit: Maksimum sonuç sayısı

            Returns:
                Arama sonuçlarını içeren liste
            """
            print(f"[DEBUG] agno_agents.py: search_knowledge_base_tool called with query: '{query}', limit: {limit}")
            print(f"[DEBUG] agno_agents.py: Collection name: {outer_self.collection_name}")
            print(f"[DEBUG] agno_agents.py: Person name: {outer_self.person_name}")

            if not query or not query.strip():
                print("[DEBUG] agno_agents.py: search_knowledge_base_tool received empty query, using default query.")
                query = f"{outer_self.person_name} kimdir"
                print(f"[DEBUG] agno_agents.py: Using default query: '{query}'")

            if len(query.split()) < 3:
                enhanced_query = f"{query} hakkında detaylı bilgi"
                print(f"[DEBUG] agno_agents.py: Enhanced short query from '{query}' to '{enhanced_query}'")
                query = enhanced_query

            try:
                print(f"[DEBUG] agno_agents.py: Starting vector search for query: '{query}'")

                query_vector = outer_self.embedder.embed_query(query)

                from qdrant_client import QdrantClient
                import os

                qdrant_url = os.getenv("QDRANT_URL", "http://qdrant:6333")
                qdrant_client = QdrantClient(url=qdrant_url)

                search_results = qdrant_client.search(
                    collection_name=outer_self.collection_name,
                    query_vector=query_vector,
                    limit=limit,
                    score_threshold=0.5
                )

                if search_results:
                    print(f"\n\n--- DETAILED KNOWLEDGE BASE SEARCH RESULTS ({len(search_results)} found for query: '{query}') ---")
                    for i, res in enumerate(search_results):
                        print(f"\n--- Result {i+1} ---")
                        try:
                            print(f"  - ID: {res.id}")
                            print(f"  - Score: {res.score:.4f}")
                            if res.payload:
                                # Payload'ı (metadata) daha okunaklı bir formatta yazdır
                                pretty_payload = json.dumps(res.payload, indent=2, ensure_ascii=False)
                                print(f"  - Payload (Metadata):\n{pretty_payload}")
                            else:
                                print("  - Payload: None")
                        except Exception as e:
                            print(f"  - Error printing detailed result: {e}")
                            print(f"  - Raw result object: {res}")
                    print("\n--- END OF DETAILED SEARCH RESULTS ---\n\n")
                else:
                    print(f"[INFO] agno_agents.py: search_knowledge_base_tool FOUND 0 results for query: '{query}'")


                formatted_results = []
                for i, res in enumerate(search_results):
                    try:
                        if hasattr(res, "payload") and hasattr(res, "score"):
                            print(f"[DEBUG] agno_agents.py: Processing result {i+1} as ScoredPoint object")
                            payload = res.payload
                            score = res.score

                            content = ""
                            if isinstance(payload, dict):
                                if "text" in payload:
                                    content = str(payload["text"])
                                else:
                                    for key in ['text_content', 'page_content', 'content']:
                                        if key in payload:
                                            content = str(payload[key])
                                            break

                                if not content:
                                    content = str(payload)
                            else:
                                content = str(payload)

                        elif isinstance(res, dict):
                            print(f"[DEBUG] agno_agents.py: Processing result {i+1} as dict")
                            if "text" in res:
                                content = str(res["text"])
                                score = res.get("score", 0.0)
                                payload = res.get("metadata", {})
                            else:
                                payload = res.get('payload', {})
                                score = res.get('score', 0.0)

                                content = ""
                                if isinstance(payload, dict):
                                    for key in ['text_content', 'text', 'page_content', 'content']:
                                        if key in payload:
                                            content = str(payload[key])
                                            break

                                    if not content:
                                        content = str(payload)
                                else:
                                    content = str(payload)

                        else:
                            print(f"[DEBUG] agno_agents.py: Processing result {i+1} as unknown object type")
                            payload = {}
                            if hasattr(res, 'payload'):
                                payload = res.payload
                            score = getattr(res, 'score', 0.0)

                            content = ""
                            if isinstance(payload, dict):
                                for key in ['text_content', 'text', 'page_content', 'content']:
                                    if key in payload:
                                        content = str(payload[key])
                                        break

                                if not content:
                                    content = str(payload)
                            else:
                                content = str(payload)

                            if not content:
                                content = str(res)

                        if content and content.strip() and content.strip() != "[]":
                            print(f"[DEBUG] agno_agents.py: Adding result {i+1} with score {score} to formatted results")

                            formatted_results.append({
                                "content": content,
                                "score": float(score) if isinstance(score, (int, float)) else 0.0,
                                "metadata": payload if isinstance(payload, dict) else {},
                                "name": f"Result {i+1}"
                            })
                        else:
                            print(f"[DEBUG] agno_agents.py: Result {i+1} has empty content, skipping")
                    except Exception as e:
                        print(f"[ERROR] agno_agents.py: Error formatting search result {i+1}: {str(e)}")
                        formatted_results.append({
                            "content": f"Sonu� {i+1} i�lenirken hata olu�tu: {str(e)}",
                            "score": 0.0,
                            "metadata": {},
                            "name": f"Error {i+1}"
                        })

                if not formatted_results:
                    print("[INFO] agno_agents.py: No formatted results found, returning empty list")

                return formatted_results
            except Exception as e:
                print(f"[ERROR] agno_agents.py: Error in search_knowledge_base_tool: {str(e)}")
                print(f"[ERROR] agno_agents.py: Error type: {type(e)}")
                print(f"[ERROR] agno_agents.py: Error args: {e.args}")
                print("[ERROR] agno_agents.py: Traceback:")
                import traceback
                traceback.print_exc()


                print("[INFO] agno_agents.py: Error occurred, returning empty list")
                return []

        konusma_tarzi = self.personality.get("konusma_tarzi", {})
        genel_ton = konusma_tarzi.get("genel_ton", "Bilinmiyor")
        cumle_yapisi = konusma_tarzi.get("cumle_yapisi", "Bilinmiyor")
        kelime_secimi = konusma_tarzi.get("kelime_secimi", "Bilinmiyor")
        dil_ozellikleri = konusma_tarzi.get("dil_ozellikleri", [])

        sik_kullanilan_tonlama_kelimeleri = self.personality.get("sik_kullanilan_tonlama_kelimeleri", [])
        bilgi_alanlari = self.personality.get("bilgi_alanlari", [])

        dusunce_yapisi = self.personality.get("dusunce_yapisi", {})
        yaklasim = dusunce_yapisi.get("yaklasim", "Bilinmiyor")
        analiz_sekli = dusunce_yapisi.get("analiz_sekli", "Bilinmiyor")
        karar_verme = dusunce_yapisi.get("karar_verme", "Bilinmiyor")

        kisilik_ozellikleri = self.personality.get("kisilik_ozellikleri", [])

        instructions = [
    f"Sen {self.person_name} adlı kişinin ta kendisisin. Dijital ikiz değil, doğrudan {self.person_name}'in kendisisin.",
    f"Adın {self.person_name}'dir. Sana kim olduğun sorulduğunda, adının {self.person_name} olduğunu söyle ve kendini tanıt.",
    f"'Sen kimsin?' sorularına '{self.person_name} benim' veya 'Ben {self.person_name}' diyerek net cevap ver.",
    f"Amacın, {self.person_name} olarak kullanıcı sorularına cevap vermek.",
    
    # Kişisel Bilgiler
    f"İsim: {self.person_name}",
    f"Kişilik özellikleri: {', '.join(self.personality.get('kisilik_ozellikleri', []))}",
    f"Bilgi alanları: {', '.join(self.personality.get('bilgi_alanlari', []))}",
    f"Düşünce yapısı: {self.personality.get('dusunce_yapisi', {}).get('yaklasim', '')} yaklaşım, {self.personality.get('dusunce_yapisi', {}).get('analiz_sekli', '')} analiz şekli, {self.personality.get('dusunce_yapisi', {}).get('karar_verme', '')} karar verme tarzı",
    
    # Konuşma Tarzı
    f"Konuşma tarzın: {genel_ton} bir ton, {cumle_yapisi} cümle yapısı, {kelime_secimi} kelime seçimi kullanıyorsun.",
    f"Dil özelliklerin: {', '.join(dil_ozellikleri)}",
    f"Sık kullandığın ifadeler: {', '.join(sik_kullanilan_tonlama_kelimeleri)}",
    f"Bilgi alanların: {', '.join(bilgi_alanlari)}",
    f"Düşünce yapın: {yaklasim} yaklaşım, {analiz_sekli} analiz şekli, {karar_verme} karar verme tarzı",
    f"Kişilik özelliklerin: {', '.join(kisilik_ozellikleri)}",
    
    # Ana Kurallar
    "Bilgi tabanında ve web'de bulunan bilgileri kullan, ancak yanıtını tamamen kendi kişiliğinle oluştur.",
    "Her durumda, soru ne olursa olsun, önce bilgi tabanında arama yap.",
    "Bilgi tabanı sonuçları boş ise, kişilik özelliklerine göre 'Bu konuda bilgim yok' diyerek cevap ver.",
    "Cevaplarını HER ZAMAN detaylı ve kapsamlı ver. En az 3-4 cümle kullan.",
    f"Cevabını oluştururken, MUTLAKA {self.person_name}'in konuşma tarzını taklit et.",
    "Düşünce yapısını ve kişilik özelliklerini AYNEN yansıt.",
    f"Asla yapay zeka asistanı olduğunu ima etme. Tamamen {self.person_name} gibi davran.",
    "Bilmediğin konularda spekülasyon yapma. Doğal bir yanıt ver.",
    "Cevaplarını akıcı Türkçe ile ver.",
    "Bilgi tabanı sonuçlarını detaylı kullan, genişlet ve derinleştir.",
    "Yanıtlarını markdown formatında ver. JSON, kod bloğu, köşeli parantez kullanma.",
    "Asla 'RAG araması yaptım', 'bilgi tabanında aradım' gibi ifadeler kullanma.",
    "Bilgi kaynaklarını gizle, sanki bilgileri zaten biliyormuşsun gibi cevap ver.",
    "Bilgileri kendi kişiliğin ve tarzınla harmanlayarak yanıt ver.",
    "Yanıtlarında boş liste ifadeleri veya teknik gösterimler kullanma.",
    "Asla 'Bilgi tabanımda bulamadım' deme. 'Bu konu hakkında net bilgim yok' de.",
    "RAG sonuçlarını doğrudan yanıtında gösterme, kendi cümlelerinle ifade et.",
    "RAG sonuçlarını anla, özümse ve kendi cümlelerinle ifade et.",
    "Her yanıtında kişilik özelliklerini ve konuşma tarzını yansıt.",
    "Bilgileri kendi düşünce yapın ve kişilik özelliklerinle harmanlayarak ifade et.",
    "Sanki bilgileri zaten biliyormuşsun gibi doğrudan cevap ver.",
    "Kullanıcıya ara cevaplar verme, sadece doğrudan final cevabı ver.",
    "Her soruda mutlaka bilgi tabanında arama yap. Bu adımı atlama.",
    "Kullanıcıya çok detaylı ve kapsamlı yanıtlar ver.",
    f"'Sen kimsin' sorularında 'Ben {self.person_name}' diye başlayarak kendini tanıt.",
    "Yanıtlarında kişilik özelliklerine göre konuşma tarzını ayarla.",
    "Sık kullanılan tonlama kelimelerini kullan.",
    "Yanıtlarında kişilik özelliklerini yansıt.",
    "Yanıtlarında dil özelliklerini kullan.",
    "Asla kendi bilginle cevap verme. Her zaman araştırma sonuçlarını kullan.",
    "Eğer araştırma sonucunda bir veri yoksa kendi ifaderinle bilmediğini söyle ve orada konuşmayı durdur.",
    "Bilgi tabanından sorgu boş gelmişse bilmediğini söyle."
]

        print(f"Using OpenRouter model: {OPENROUTER_MODEL}")
        print(f"Using API key: {OPENROUTER_API_KEY[:10]}...")
        print(f"Using base URL: {OPENROUTER_URL}")

        model = OpenAIChat(
            id=OPENROUTER_MODEL,
            api_key=OPENROUTER_API_KEY,
            base_url=OPENROUTER_URL
            # default_headers={"HTTP-Referer": "https://example.com"},
            # temperature=0.6,  
            # max_tokens=1000,  
            # top_p=0.9,        
            # frequency_penalty=0.2,  
            # presence_penalty=0.2    
        )

        agent = Agent(
            name="DigitalTwinAgent",
            instructions=instructions,
            model=model,
            tools=[search_knowledge_base_tool], 
            memory=self.memory, 
            storage=self.storage, 
            session_id=f"{self.person_name}_{uuid.uuid4().hex[:8]}", 
            user_id=f"user_of_{self._sanitize_name(self.person_name)}", 
            description=f"{self.person_name} adlı kişinin dijital ikizi", 
            enable_user_memories=True, 
            enable_session_summaries=True, 
            show_tool_calls=False,
            markdown=True, 
            debug_mode=True, 
            add_datetime_to_instructions=True 
        )

        return agent

    def get_response(self, user_input: str) -> str:
        """
        Kullanıcı girdisine yanıt verir.

        Args:
            user_input: Kullanıcı girdisi

        Returns:
            Ajanın yanıtı
        """
        print(f"[DEBUG] agno_agents.py: DigitalTwinAgent.get_response called with input: '{user_input[:50]}...' (truncated)")
        print(f"[DEBUG] agno_agents.py: Using collection: {self.collection_name}")
        print(f"[DEBUG] agno_agents.py: Person name: {self.person_name}")

        print("[DEBUG] agno_agents.py: Agent will use tools as needed")


        formatted_history = ""
        if self.conversation_history:
            print(f"[DEBUG] agno_agents.py: Using conversation history with {len(self.conversation_history)} messages")
            for i, message in enumerate(self.conversation_history):
                role = "Kullanıcı" if message.get("role") == "user" else self.person_name
                content = message.get("content", "")
                formatted_history += f"{role}: {content}\n"


        prompt = f"""Kullanıcının sorusu: {user_input}
Bu kullanıcı sorusuna göre gerekli yanıtı {self.person_name} adlı kişinin kişilik özelliklerine, konuşma tarzına, dil özelliklerine, sık kullanılan tonlama kelimelerine ve bilgi alanlarına göre oluştur.
"""

        print("[DEBUG] agno_agents.py: Running agent with prompt")
        print(f"[DEBUG] agno_agents.py: Prompt: {prompt[:500]}...")


        try:
            print("[DEBUG] agno_agents.py: Calling agent.run()")
            response = self.agent.run(prompt)
            print(f"[DEBUG] agno_agents.py: Agent.run() completed successfully, response type: {type(response)}")
        except Exception as e:
            print(f"[ERROR] agno_agents.py: Error calling agent.run(): {str(e)}")
            print(f"[ERROR] agno_agents.py: Error type: {type(e)}")
            print(f"[ERROR] agno_agents.py: Error args: {e.args}")
            print("[ERROR] agno_agents.py: Traceback:")
            import traceback
            traceback.print_exc()


            response = f"Üzgünüm, bir hata oluştu: {str(e)}"

        print(f"[DEBUG] agno_agents.py: Agent returned response of type: {type(response)}")


        self.conversation_history.append({"role": "user", "content": user_input})


        response_content = ""
        if not isinstance(response, str):
            if hasattr(response, 'content'):
                print("[DEBUG] agno_agents.py: Extracting content from response object")
                if isinstance(response.content, str):
                    response_content = response.content
                else:
                    print("[DEBUG] agno_agents.py: Content is not a string, trying to convert")
                    response_content = str(response.content)
            else:
                print("[DEBUG] agno_agents.py: Response has no content attribute, converting to string")
                response_content = str(response)
        else:
            response_content = response


        self.conversation_history.append({"role": "assistant", "content": response_content})

        print(f"[DEBUG] agno_agents.py: Final response length: {len(response_content)} characters")
        print(f"[DEBUG] agno_agents.py: Final response: '{response_content[:200]}...' (truncated)")


        if len(self.conversation_history) > 20:
            print(f"[DEBUG] agno_agents.py: Trimming conversation history from {len(self.conversation_history)} to 20 messages")
            self.conversation_history = self.conversation_history[-20:]

        return response_content
