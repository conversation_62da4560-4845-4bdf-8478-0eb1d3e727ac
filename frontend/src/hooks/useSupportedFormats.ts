import { useState, useEffect } from 'react';
import apiClient from '../api/client';

interface SupportedFormat {
  total_supported: number;
  all_formats: string[];
  categories: {
    office: string[];
    pdf: string[];
    images: string[];
    text: string[];
    web: string[];
    data: string[];
    ebook: string[];
    libreoffice: string[];
  };
  features: {
    ocr_support: string;
    pdf_advanced: string;
    office_full: string;
    web_parsing: string;
    data_formats: string;
    llamaindex: string;
  };
}

export const useSupportedFormats = () => {
  const [supportedFormats, setSupportedFormats] = useState<SupportedFormat | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSupportedFormats = async () => {
      try {
        setLoading(true);
        const response = await apiClient.get('/documents/supported-formats');
        setSupportedFormats(response.data);
      } catch (err: any) {
        setError(err.response?.data?.detail || 'Desteklenen formatlar yüklenemedi');
      } finally {
        setLoading(false);
      }
    };

    fetchSupportedFormats();
  }, []);

  // Accept attribute için formatları düzenle
  const getAcceptAttribute = () => {
    if (!supportedFormats) return '';
    return supportedFormats.all_formats.join(',');
  };

  // Kategorilere göre formatları gruplayıp göster
  const getFormattedCategories = () => {
    if (!supportedFormats) return {};
    
    const categoryNames = {
      office: 'Office Dokümanları',
      pdf: 'PDF Dosyaları',
      images: 'Resim Dosyaları (OCR)',
      text: 'Metin Dosyaları', 
      web: 'Web Dosyaları',
      data: 'Veri Dosyaları',
      ebook: 'E-kitap Dosyaları',
      libreoffice: 'LibreOffice Dosyaları'
    };

    return Object.entries(supportedFormats.categories).reduce((acc, [key, formats]) => {
      if (formats.length > 0) {
        acc[categoryNames[key as keyof typeof categoryNames] || key] = formats;
      }
      return acc;
    }, {} as Record<string, string[]>);
  };

  return {
    supportedFormats,
    loading,
    error,
    getAcceptAttribute,
    getFormattedCategories,
    totalSupported: supportedFormats?.total_supported || 0
  };
}; 