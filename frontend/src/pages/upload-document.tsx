import React, { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Button,
  Typography,
  Paper,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  FormHelperText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Divider,
  Card,
  CardContent,
  Grid,
  Collapse,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Badge,
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  InsertDriveFile as FileIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  HourglassEmpty as ProcessingIcon,
  Delete as DeleteIcon,
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  AutoAwesome as AutoAwesomeIcon,
} from '@mui/icons-material';
import apiClient from '../api/client';
import { WithAuth } from '../contexts/AuthContext';
import { useAuth } from '../contexts/AuthContext';
import { useSupportedFormats } from '../hooks/useSupportedFormats';

interface DigitalTwin {
  id: number;
  name: string;
}

interface Document {
  id: string;
  filename: string;
  status: string;
}

interface FileUploadStatus {
  file: File;
  id?: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'failed';
  error?: string;
  progress?: number;
}

interface MultipleDocumentsResponse {
  documents: Document[];
  total_uploaded: number;
  total_failed: number;
  failed_files: string[];
}

const UploadDocument: React.FC = () => {
  const router = useRouter();
  const { isAdmin } = useAuth();
  const [digitalTwins, setDigitalTwins] = useState<DigitalTwin[]>([]);
  const [selectedDigitalTwin, setSelectedDigitalTwin] = useState<number | ''>('');
  const [selectedFiles, setSelectedFiles] = useState<FileUploadStatus[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [loadingTwins, setLoadingTwins] = useState<boolean>(true);
  const [success, setSuccess] = useState<boolean>(false);
  const [processingDialogOpen, setProcessingDialogOpen] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [showSupportedFormats, setShowSupportedFormats] = useState<boolean>(false);

  const selectedFilesRef = useRef<FileUploadStatus[]>([]);
  
  // Desteklenen formatları al
  const { 
    supportedFormats, 
    loading: formatsLoading, 
    error: formatsError,
    getAcceptAttribute,
    getFormattedCategories,
    totalSupported
  } = useSupportedFormats();

  useEffect(() => {
    if (!isAdmin) {
      router.push('/');
    }
  }, [isAdmin, router]);

  useEffect(() => {
    const fetchDigitalTwins = async () => {
      try {
        setLoadingTwins(true);
        const response = await apiClient.get('/digital-twins');
        setDigitalTwins(response.data);
      } catch (err: any) {
        setError(err.response?.data?.detail || 'Dijital ikizler yüklenirken bir hata oluştu.');
      } finally {
        setLoadingTwins(false);
      }
    };

    if (isAdmin) {
      fetchDigitalTwins();
    }
  }, [isAdmin]);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (processingDialogOpen) {
      const startPolling = () => {
      interval = setInterval(async () => {
        try {
            const currentFiles = selectedFilesRef.current;
            const fileIds = currentFiles.filter(f => f.id).map(f => f.id!);
          
            if (fileIds.length === 0) {
              const hasProcessingFiles = currentFiles.some(f => f.status === 'processing' || f.status === 'uploading');
              if (!hasProcessingFiles) {
                setProcessingDialogOpen(false);
                router.push('/');
                return;
              }
              return;
            }
          
          const response = await apiClient.post('/documents/status/multiple', fileIds);
          const data = response.data;
          
            const updatedFiles = currentFiles.map(file => {
            if (file.id) {
              const statusInfo = data.document_statuses.find((s: any) => s.id === file.id);
              if (statusInfo) {
                  const validStatus = statusInfo.status as 'pending' | 'uploading' | 'processing' | 'completed' | 'failed';
                  return { 
                    ...file, 
                    status: validStatus,
                    progress: statusInfo.progress || 0
                  } as FileUploadStatus;
              }
            }
            return file;
            });

            selectedFilesRef.current = updatedFiles;
            setSelectedFiles([...updatedFiles]);

            if (data.is_all_done) {
            setTimeout(() => {
              setProcessingDialogOpen(false);
              router.push('/');
              }, 3000);
          }

        } catch (error) {
          // Error during status polling
        }
        }, 3000);
      };

      setTimeout(startPolling, 3000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [processingDialogOpen, router]);

  useEffect(() => {
    selectedFilesRef.current = selectedFiles;
  }, [selectedFiles]);

  const handleDigitalTwinChange = (event: any) => {
    setSelectedDigitalTwin(event.target.value);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const newFiles = Array.from(event.target.files).map(file => ({
            file,
        status: 'pending' as const,
        progress: 0
      }));
      setSelectedFiles(newFiles);
    }
  };

  const removeFile = (index: number) => {
    setSelectedFiles(files => files.filter((_, i) => i !== index));
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setError(null);
    setSuccess(false);

    if (!selectedDigitalTwin) {
      setError('Lütfen bir dijital ikiz seçin');
      return;
    }

    if (selectedFiles.length === 0) {
      setError('Lütfen en az bir dosya seçin');
      return;
    }

    try {
      setLoading(true);
      setUploadProgress(0);

      setSelectedFiles(files => files.map(f => ({ ...f, status: 'uploading' as const })));

      const formData = new FormData();
      formData.append('digital_twin_id', selectedDigitalTwin.toString());
      selectedFiles.forEach(fileStatus => {
        formData.append('files', fileStatus.file);
      });

      const response = await apiClient.post('/documents/multiple', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            setUploadProgress(percentCompleted);
          }
        },
      });

      const data: MultipleDocumentsResponse = response.data;
      
      const updatedFiles = selectedFiles.map(fileStatus => {
        const matchingDoc = data.documents.find(doc => doc.filename === fileStatus.file.name);
          return {
            ...fileStatus,
          id: matchingDoc?.id,
          status: matchingDoc ? 'processing' as const : 'failed' as const,
          error: matchingDoc ? undefined : 'Upload failed'
        };
      });

      setSelectedFiles(updatedFiles);
      selectedFilesRef.current = updatedFiles;

      setProcessingDialogOpen(true);
      setSuccess(true);

    } catch (err: any) {
      console.error('Upload error:', err);
      
      // Timeout hatası için özel mesaj
      if (err.code === 'ECONNABORTED' || err.message?.includes('timeout')) {
        setError('Yükleme işlemi zaman aşımına uğradı. OCR modellerinin yüklenmesi biraz zaman alabilir. Lütfen birkaç dakika bekleyin ve tekrar deneyin.');
      } else {
        setError(err.response?.data?.detail || 'Dosya yüklenirken bir hata oluştu.');
      }
      
      setSelectedFiles(files => files.map(f => ({ ...f, status: 'failed' as const })));
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon color="success" />;
      case 'failed':
        return <ErrorIcon color="error" />;
      case 'processing':
        return <ProcessingIcon color="warning" />;
      default:
        return <FileIcon />;
    }
  };

  const getStatusChip = (status: string) => {
    switch (status) {
      case 'pending':
        return <Chip label="Bekliyor" size="small" />;
      case 'uploading':
        return <Chip label="Yükleniyor" color="primary" size="small" />;
      case 'processing':
        return <Chip label="İşleniyor" color="warning" size="small" />;
      case 'completed':
        return <Chip label="Tamamlandı" color="success" size="small" />;
      case 'failed':
        return <Chip label="Başarısız" color="error" size="small" />;
      default:
        return <Chip label="Bilinmiyor" size="small" />;
    }
  };

  if (!isAdmin) {
    return null;
  }

  return (
    <Box sx={{ padding: 3 }}>
      <Paper elevation={3} sx={{ padding: 4, maxWidth: 900, margin: 'auto' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 2, marginBottom: 3 }}>
          <AutoAwesomeIcon color="primary" fontSize="large" />
          <Typography variant="h4" component="h1" align="center">
            Gelişmiş Döküman Yükle
        </Typography>
        </Box>
        
        <Box sx={{ textAlign: 'center', marginBottom: 3 }}>
          <Typography variant="subtitle1" color="textSecondary">
            LlamaIndex parser ile 20+ dosya formatını destekliyoruz
          </Typography>
          <Typography variant="caption" color="textSecondary">
            OCR, gelişmiş PDF işleme, Office dokümanları ve daha fazlası
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ marginBottom: 2 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ marginBottom: 2 }}>
            Dosyalar başarıyla yüklendi ve işlenmek üzere kuyruğa eklendi.
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <FormControl fullWidth margin="normal" required disabled={loadingTwins}>
            <InputLabel>Dijital İkiz</InputLabel>
              <Select
                value={selectedDigitalTwin}
              onChange={handleDigitalTwinChange}
                label="Dijital İkiz"
              >
                {digitalTwins.map((twin) => (
                  <MenuItem key={twin.id} value={twin.id}>
                    {twin.name}
                  </MenuItem>
                ))}
              </Select>
            {loadingTwins && (
              <FormHelperText>
                <CircularProgress size={16} sx={{ marginRight: 1 }} />
                Dijital ikizler yükleniyor...
              </FormHelperText>
            )}
            </FormControl>

          <Box sx={{ marginTop: 3, marginBottom: 2 }}>
            {/* Desteklenen formatlar bilgisi */}
            <Card sx={{ marginBottom: 2, backgroundColor: 'rgba(25, 118, 210, 0.04)' }}>
              <CardContent sx={{ padding: '16px !important' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AutoAwesomeIcon color="primary" fontSize="small" />
                    <Typography variant="subtitle2" color="primary">
                      LlamaIndex Gelişmiş Parser
                    </Typography>
                    <Badge badgeContent={totalSupported} color="primary" max={99}>
                      <Typography variant="caption" color="textSecondary">
                        format
                      </Typography>
                    </Badge>
                  </Box>
                  <Button 
                    size="small" 
                    onClick={() => setShowSupportedFormats(!showSupportedFormats)}
                    endIcon={<ExpandMoreIcon sx={{ transform: showSupportedFormats ? 'rotate(180deg)' : 'none' }} />}
                  >
                    Desteklenen Formatlar
                  </Button>
                </Box>
                
                <Collapse in={showSupportedFormats}>
                  <Box sx={{ marginTop: 2 }}>
                    {formatsLoading ? (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CircularProgress size={16} />
                        <Typography variant="caption">Formatlar yükleniyor...</Typography>
                      </Box>
                    ) : formatsError ? (
                      <Alert severity="warning">
                        {formatsError}
                      </Alert>
                    ) : (
                      <Grid container spacing={2}>
                        {Object.entries(getFormattedCategories()).map(([category, formats]) => (
                          <Grid item xs={12} sm={6} md={4} key={category}>
                            <Box sx={{ padding: 1, border: '1px solid #e0e0e0', borderRadius: 1 }}>
                              <Typography variant="caption" fontWeight="bold" color="primary">
                                {category}
                              </Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, marginTop: 0.5 }}>
                                {formats.map((format) => (
                                  <Chip
                                    key={format}
                                    label={format}
                                    size="small"
                                    variant="outlined"
                                    sx={{ fontSize: '0.7rem', height: '20px' }}
                                  />
                                ))}
                              </Box>
                            </Box>
                          </Grid>
                        ))}
                      </Grid>
                    )}
                    
                    {supportedFormats && (
                      <Box sx={{ marginTop: 2, padding: 1, backgroundColor: 'rgba(0,0,0,0.02)', borderRadius: 1 }}>
                        <Typography variant="caption" color="textSecondary">
                          <strong>🔥 Özellikler:</strong> {Object.values(supportedFormats.features).join(' • ')}
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </Collapse>
              </CardContent>
            </Card>

            {/* Dosya seçme butonu */}
              <input
                type="file"
              accept={getAcceptAttribute()}
                multiple
                onChange={handleFileChange}
              style={{ display: 'none' }}
              id="file-upload"
              disabled={loading || formatsLoading}
              />
              <label htmlFor="file-upload">
                <Button
                  variant="outlined"
                  component="span"
                startIcon={<CloudUploadIcon />}
                disabled={loading || formatsLoading}
                  fullWidth
                sx={{ padding: 2 }}
                >
                {formatsLoading 
                  ? 'Formatlar yükleniyor...' 
                  : `Dosya Seç (${totalSupported} Format Destekleniyor)`
                }
                </Button>
              </label>
          </Box>

              {selectedFiles.length > 0 && (
            <Card sx={{ marginBottom: 3 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Seçilen Dosyalar ({selectedFiles.length})
                    </Typography>
                <List>
                      {selectedFiles.map((fileStatus, index) => (
                        <React.Fragment key={index}>
                      <ListItem>
                            <ListItemIcon>
                              {getStatusIcon(fileStatus.status)}
                            </ListItemIcon>
                            <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body2">
                                {fileStatus.file.name}
                              </Typography>
                              {getStatusChip(fileStatus.status)}
                            </Box>
                          }
                              secondary={
                            <Box sx={{ marginTop: 1 }}>
                              <Typography variant="caption" color="textSecondary">
                                {(fileStatus.file.size / 1024 / 1024).toFixed(2)} MB
                              </Typography>
                              {(fileStatus.status === 'processing' || fileStatus.status === 'uploading') && (
                                <Box sx={{ width: '100%', marginTop: 1 }}>
                                  <LinearProgress 
                                    variant="determinate" 
                                    value={fileStatus.progress || 0}
                                  />
                                  <Typography variant="caption" color="textSecondary">
                                    %{fileStatus.progress || 0}
                                  </Typography>
                                </Box>
                              )}
                              {fileStatus.error && (
                                <Typography variant="caption" color="error">
                                  Hata: {fileStatus.error}
                                </Typography>
                              )}
                            </Box>
                              }
                            />
                        <IconButton
                          edge="end"
                          onClick={() => removeFile(index)}
                          disabled={loading}
                        >
                          <DeleteIcon />
                        </IconButton>
                          </ListItem>
                          {index < selectedFiles.length - 1 && <Divider />}
                        </React.Fragment>
                      ))}
                    </List>
                  </CardContent>
                </Card>
              )}

            <Button
              type="submit"
            variant="contained"
            color="primary"
            disabled={loading || selectedFiles.length === 0 || !selectedDigitalTwin}
              fullWidth
            sx={{ padding: 1.5 }}
            >
              {loading ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CircularProgress size={20} color="inherit" />
                Yükleniyor... %{uploadProgress}
              </Box>
              ) : (
              `${selectedFiles.length} Dosyayı Yükle`
              )}
            </Button>
        </form>
      </Paper>

      <Dialog
        open={processingDialogOpen}
        disableEscapeKeyDown
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ProcessingIcon color="primary" />
            Dosyalar İşleniyor
          </Box>
        </DialogTitle>
          <DialogContent>
          <DialogContentText sx={{ marginBottom: 3 }}>
            Dosyalarınız yüklendi ve şu anda dijital ikize entegre ediliyor. 
            Bu işlem dosya boyutuna bağlı olarak birkaç dakika sürebilir.
            </DialogContentText>
            
            <Grid container spacing={2}>
              {selectedFiles.map((fileStatus, index) => (
              <Grid item xs={12} key={index}>
                <Paper sx={{ padding: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, marginBottom: 1 }}>
                        {getStatusIcon(fileStatus.status)}
                    <Typography variant="body2" sx={{ flexGrow: 1 }}>
                            {fileStatus.file.name}
                          </Typography>
                          {getStatusChip(fileStatus.status)}
                        </Box>
                  
                  {(fileStatus.status === 'processing' || fileStatus.status === 'uploading') && (
                    <Box sx={{ width: '100%', marginTop: 1 }}>
                      <LinearProgress 
                        variant="determinate" 
                        value={fileStatus.progress || 0}
                      />
                      <Typography variant="caption" color="textSecondary" sx={{ marginTop: 0.5 }}>
                        %{fileStatus.progress || 0} tamamlandı
                      </Typography>
                      </Box>
                  )}
                  
                  {fileStatus.error && (
                    <Typography variant="caption" color="error" sx={{ display: 'block', marginTop: 1 }}>
                      Hata: {fileStatus.error}
                    </Typography>
                  )}
                </Paper>
                </Grid>
              ))}
            </Grid>

          <Box sx={{ marginTop: 3, padding: 2, backgroundColor: 'rgba(0,0,0,0.05)', borderRadius: 1 }}>
            <Typography variant="body2" color="textSecondary">
              💡 İpucu: Bu pencereyi kapatabilirsiniz, işlem arka planda devam edecek.
              </Typography>
            </Box>
          </DialogContent>
        </Dialog>
    </Box>
  );
};

const ProtectedUploadDocument = () => (
  <WithAuth>
    <UploadDocument />
  </WithAuth>
);

export default ProtectedUploadDocument;
