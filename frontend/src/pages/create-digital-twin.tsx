import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { useForm } from 'react-hook-form';
import {
  Box,
  Button,
  TextField,
  Typography,
  Paper,
  Alert,

  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
} from '@mui/material';
import apiClient from '../api/client';
import { WithAuth } from '../contexts/AuthContext';
import { useAuth } from '../contexts/AuthContext';

interface CreateDigitalTwinFormData {
  name: string;
  youtube_url: string;
}

const CreateDigitalTwin: React.FC = () => {
  const router = useRouter();
  const { isAdmin } = useAuth();
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const [digitalTwinId, setDigitalTwinId] = useState<string | null>(null);
  const [processingStatus, setProcessingStatus] = useState<string | null>(null);
  const [processingDialogOpen, setProcessingDialogOpen] = useState(false);

  // Redirect to home page if not admin
  useEffect(() => {
    if (!isAdmin) {
      router.push('/');
    }
  }, [isAdmin, router]);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<CreateDigitalTwinFormData>();

  // Poll for digital twin status
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (digitalTwinId && processingStatus === "processing") {
      intervalId = setInterval(async () => {
        try {
          const response = await apiClient.get(`/digital-twins/${digitalTwinId}/status`);
          setProcessingStatus(response.data.status);

          if (response.data.status === "completed") {
            clearInterval(intervalId);
            setTimeout(() => {
              setProcessingDialogOpen(false);
              router.push('/');
            }, 2000);
          } else if (response.data.status === "failed") {
            clearInterval(intervalId);
            setProcessingStatus("failed");
          }
        } catch (error) {
          // Error checking status
        }
      }, 3000);
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [digitalTwinId, processingStatus, router]);

  const onSubmit = async (data: CreateDigitalTwinFormData) => {
    try {
      setLoading(true);
      // Always use Turkish language
      const payload = { ...data, language: 'tr' };
      const response = await apiClient.post('/digital-twins', payload);
      setDigitalTwinId(response.data.id);
      setProcessingDialogOpen(true);
      setProcessingStatus("processing");
      setSuccess(true);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Dijital ikiz oluşturulurken bir hata oluştu.');
      setLoading(false);
    }
  };

  // Show empty page if not admin (while redirecting)
  if (!isAdmin) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <Alert severity="warning">
          You don't have access to this page. Redirecting to home page...
        </Alert>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '70vh',
      }}
    >
      <Paper elevation={3} sx={{ p: 4, width: '100%', maxWidth: 600 }}>
        <Typography variant="h4" component="h1" gutterBottom align="center">
          Yeni Dijital İkiz Oluştur
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {success && !processingDialogOpen && (
          <Alert severity="success" sx={{ mb: 2 }}>
            Dijital ikiz oluşturuldu! Dijital ikizler sayfasına yönlendiriliyorsunuz...
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit(onSubmit)} noValidate>
          <TextField
            margin="normal"
            required
            fullWidth
            id="name"
            label="Dijital İkiz Adı"
            autoFocus
            disabled={loading || processingDialogOpen}
            {...register('name', { required: 'Dijital ikiz adı gerekli' })}
            error={!!errors.name}
            helperText={errors.name?.message}
          />

          <TextField
            margin="normal"
            required
            fullWidth
            id="youtube_url"
            label="YouTube Video URL'si"
            placeholder="https://www.youtube.com/watch?v=..."
            disabled={loading || processingDialogOpen}
            {...register('youtube_url', {
              required: 'YouTube URL gerekli',
              pattern: {
                value: /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+$/,
                message: 'Geçerli bir YouTube URL\'si girin',
              }
            })}
            error={!!errors.youtube_url}
            helperText={errors.youtube_url?.message}
          />

          {/* Dil seçimi kaldırıldı - sadece Türkçe destekleniyor */}

          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2 }}
            disabled={loading || processingDialogOpen}
          >
            {loading ? <CircularProgress size={24} /> : 'Dijital İkiz Oluştur'}
          </Button>

          <Button
            fullWidth
            variant="text"
            onClick={() => router.push('/')}
            disabled={loading || processingDialogOpen}
          >
            İptal
          </Button>
        </Box>
      </Paper>

      {/* Processing Dialog */}
      <Dialog
        open={processingDialogOpen}
        disableEscapeKeyDown
        onClose={(event, reason) => {
          if (reason !== 'backdropClick' && processingStatus === 'completed') {
            setProcessingDialogOpen(false);
          }
        }}
      >
        <DialogTitle>
          {processingStatus === "processing" && "Dijital İkiz Oluşturuluyor"}
          {processingStatus === "completed" && "Dijital İkiz Oluşturuldu"}
          {processingStatus === "failed" && "Dijital İkiz Oluşturulamadı"}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', p: 2 }}>
            {processingStatus === "processing" && (
              <>
                <CircularProgress sx={{ mb: 2 }} />
                <DialogContentText>
                  Dijital ikiz oluşturuluyor. Bu işlem birkaç dakika sürebilir. Lütfen bekleyin...
                </DialogContentText>
              </>
            )}
            {processingStatus === "completed" && (
              <DialogContentText>
                Dijital ikiz başarıyla oluşturuldu! Dijital ikizler sayfasına yönlendiriliyorsunuz...
              </DialogContentText>
            )}
            {processingStatus === "failed" && (
              <>
                <DialogContentText color="error">
                  Dijital ikiz oluşturulurken bir hata oluştu. Lütfen tekrar deneyin.
                </DialogContentText>
                <Button
                  variant="contained"
                  onClick={() => setProcessingDialogOpen(false)}
                  sx={{ mt: 2 }}
                >
                  Kapat
                </Button>
              </>
            )}
          </Box>
        </DialogContent>
      </Dialog>
    </Box>
  );
};

const ProtectedCreateDigitalTwin = () => (
  <WithAuth>
    <CreateDigitalTwin />
  </WithAuth>
);

export default ProtectedCreateDigitalTwin;
