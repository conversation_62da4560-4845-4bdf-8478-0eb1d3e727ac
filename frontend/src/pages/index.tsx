import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Grid,
  CircularProgress,
  Alert,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Snackbar,
  Chip,
  List,
  ListItem,
  ListItemText,
  Divider,
  Collapse,
  IconButton,
  Tooltip,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import DeleteIcon from '@mui/icons-material/Delete';
import apiClient from '../api/client';
import { WithAuth, useAuth } from '../contexts/AuthContext';
import { useSupportedFormats } from '../hooks/useSupportedFormats';

interface DigitalTwin {
  id: string;
  name: string;
  collection_name: string;
  youtube_url: string | null;
  created_at: string;
}

interface Document {
  id: number;
  digital_twin_id: number;
  filename: string;
  status: string;
  created_at: string;
}

// Main page showing digital twins
const Home: React.FC = () => {
  const router = useRouter();
  const { isAdmin } = useAuth();
  const [digitalTwins, setDigitalTwins] = useState<DigitalTwin[]>([]);
  const [documents, setDocuments] = useState<{ [key: number]: Document[] }>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [twinToDelete, setTwinToDelete] = useState<DigitalTwin | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState<boolean>(false);
  const [snackbarMessage, setSnackbarMessage] = useState<string>('');
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({});
  const [deleteDocumentDialogOpen, setDeleteDocumentDialogOpen] = useState<boolean>(false);
  const [documentToDelete, setDocumentToDelete] = useState<Document | null>(null);
  
  // Desteklenen formatları al
  const { supportedFormats, totalSupported } = useSupportedFormats();

  const fetchDigitalTwins = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/digital-twins');
      setDigitalTwins(response.data);

      // Get documents for each digital twin (admin only)
      const documentsData: { [key: number]: Document[] } = {};
      if (isAdmin) {
        for (const twin of response.data) {
          try {
            const docsResponse = await apiClient.get(`/documents?digital_twin_id=${twin.id}`);
            documentsData[twin.id] = docsResponse.data;
          } catch (docErr) {
            documentsData[twin.id] = [];
          }
        }
      }
      setDocuments(documentsData);
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Dijital ikizler yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDigitalTwins();
  }, [isAdmin]);

  const handleStartChat = (digitalTwinId: number | string) => {
    router.push(`/chat/${digitalTwinId}`);
  };

  const handleDeleteClick = (digitalTwin: DigitalTwin) => {
    setTwinToDelete(digitalTwin);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!twinToDelete) return;

    try {
      setLoading(true);
      const response = await apiClient.delete(`/digital-twins/${twinToDelete.id}`);

      // Show success message
      setSnackbarMessage(response.data.message || 'Digital twin deleted successfully.');
      setSnackbarOpen(true);

      // Refresh the list
      fetchDigitalTwins();
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Dijital ikiz silinirken bir hata oluştu.');
    } finally {
      setDeleteDialogOpen(false);
      setTwinToDelete(null);
      setLoading(false);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setTwinToDelete(null);
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  const handleCreateDigitalTwin = () => {
    router.push('/create-digital-twin');
  };

  const handleUploadDocument = () => {
    router.push('/upload-document');
  };

  const handleExpandClick = (digitalTwinId: number | string) => {
    setExpandedCards((prev: { [key: number]: boolean }) => ({
      ...prev,
      [digitalTwinId]: !prev[digitalTwinId]
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Tamamlandı';
      case 'processing':
        return 'İşleniyor';
      case 'failed':
        return 'Başarısız';
      default:
        return status;
    }
  };

  const handleDeleteDocument = (document: Document) => {
    setDocumentToDelete(document);
    setDeleteDocumentDialogOpen(true);
  };

  const handleDeleteDocumentConfirm = async () => {
    if (!documentToDelete) return;

    try {
      setLoading(true);
      const response = await apiClient.delete(`/documents/${documentToDelete.id}`);

      // Show success message
      setSnackbarMessage(response.data.message || 'Document deleted successfully.');
      setSnackbarOpen(true);

      // Refresh the list
      fetchDigitalTwins();
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Doküman silinirken bir hata oluştu.');
    } finally {
      setDeleteDocumentDialogOpen(false);
      setDocumentToDelete(null);
      setLoading(false);
    }
  };

  const handleDeleteDocumentCancel = () => {
    setDeleteDocumentDialogOpen(false);
    setDocumentToDelete(null);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Dijital İkiz Sistemi
        </Typography>

        {isAdmin && (
        <Box>
          <Button
            variant="contained"
            color="primary"
            onClick={handleCreateDigitalTwin}
            sx={{ mr: 2 }}
          >
            Yeni Dijital İkiz Oluştur
          </Button>
          <Button
            variant="outlined"
            color="primary"
            onClick={handleUploadDocument}
          >
            Gelişmiş Doküman Yükle ({totalSupported} Format)
          </Button>
        </Box>
        )}
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}



      {digitalTwins.length === 0 ? (
        <Alert severity="info">
          {isAdmin 
            ? 'Henüz hiç dijital ikiz oluşturulmamış. "Yeni Dijital İkiz Oluştur" butonuna tıklayarak ilk dijital ikizinizi oluşturabilirsiniz.'
            : 'Henüz hiç dijital ikiz bulunmuyor.'
          }
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {digitalTwins.map((digitalTwin: DigitalTwin) => {
            const twinDocuments = documents[digitalTwin.id] || [];
            const isExpanded = expandedCards[digitalTwin.id] || false;

            return (
            <Grid item xs={12} sm={6} md={4} key={digitalTwin.id}>
              <Card>
                <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Box>
                  <Typography variant="h5" component="div">
                    {digitalTwin.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Oluşturulma Tarihi: {new Date(digitalTwin.created_at).toLocaleDateString()}
                  </Typography>
                      </Box>
                      {isAdmin && twinDocuments.length > 0 && (
                        <IconButton
                          onClick={() => handleExpandClick(digitalTwin.id)}
                          aria-expanded={isExpanded}
                          aria-label="show documents"
                          size="small"
                        >
                          {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        </IconButton>
                      )}
                    </Box>

                    {isAdmin && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Doküman Sayısı: {twinDocuments.length}
                        </Typography>
                      </Box>
                    )}

                    {isAdmin && (
                      <Collapse in={isExpanded} timeout="auto" unmountOnExit>
                        <Box sx={{ mt: 2 }}>
                          <Divider sx={{ mb: 1 }} />
                          <Typography variant="subtitle2" gutterBottom>
                            Yüklenen Dokümanlar:
                          </Typography>
                          {twinDocuments.length > 0 ? (
                            <List dense>
                              {twinDocuments.map((doc: Document) => (
                                <ListItem
                                  key={doc.id}
                                  sx={{
                                    px: 0,
                                    alignItems: 'flex-start',
                                    flexDirection: 'column',
                                    gap: 1
                                  }}
                                >
                                  <Box sx={{
                                    display: 'flex',
                                    width: '100%',
                                    alignItems: 'flex-start',
                                    gap: 1
                                  }}>
                                    <ListItemText
                                      primary={
                                        <Tooltip title={doc.filename} arrow placement="top">
                                          <Typography
                                            variant="body2"
                                            sx={{
                                              wordBreak: 'break-word',
                                              whiteSpace: 'normal',
                                              lineHeight: 1.4,
                                              cursor: 'help'
                                            }}
                                          >
                                            {doc.filename}
                                          </Typography>
                                        </Tooltip>
                                      }
                                      secondary={`Yüklenme: ${new Date(doc.created_at).toLocaleDateString()}`}
                                      sx={{ flex: 1, minWidth: 0 }}
                                    />
                                    <Box sx={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      gap: 1,
                                      flexShrink: 0
                                    }}>
                                      <Chip
                                        label={getStatusText(doc.status)}
                                        color={getStatusColor(doc.status) as any}
                                        size="small"
                                      />
                                      <IconButton
                                        size="small"
                                        color="error"
                                        onClick={() => handleDeleteDocument(doc)}
                                        aria-label="delete document"
                                      >
                                        <DeleteIcon fontSize="small" />
                                      </IconButton>
                                    </Box>
                                  </Box>
                                </ListItem>
                              ))}
                            </List>
                          ) : (
                            <Typography variant="body2" color="text.secondary">
                              Henüz doküman yüklenmemiş.
                            </Typography>
                          )}
                        </Box>
                      </Collapse>
                    )}
                </CardContent>
                <CardActions sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Button
                    size="small"
                    color="primary"
                    onClick={() => handleStartChat(digitalTwin.id)}
                  >
                    Sohbet Başlat
                  </Button>
                    {isAdmin && (
                  <Button
                    size="small"
                    color="error"
                    onClick={() => handleDeleteClick(digitalTwin)}
                  >
                    Sil
                  </Button>
                    )}
                </CardActions>
              </Card>
            </Grid>
            );
          })}
        </Grid>
      )}

      {/* Silme Onay Dialogu */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          Dijital İkizi Sil
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            "{twinToDelete?.name}" adlı dijital ikizi silmek istediğinizden emin misiniz?
            Bu işlem geri alınamaz ve tüm ilgili veriler (dokümanlar, sohbet geçmişi) silinecektir.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>İptal</Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Sil
          </Button>
        </DialogActions>
      </Dialog>

      {/* Doküman Silme Onay Dialogu */}
      <Dialog
        open={deleteDocumentDialogOpen}
        onClose={handleDeleteDocumentCancel}
        aria-labelledby="delete-document-dialog-title"
        aria-describedby="delete-document-dialog-description"
      >
        <DialogTitle id="delete-document-dialog-title">
          Dokümanı Sil
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="delete-document-dialog-description">
            "{documentToDelete?.filename}" adlı dokümanı silmek istediğinizden emin misiniz?
            Bu işlem geri alınamaz ve doküman hem veritabanından hem de vektör veritabanından kalıcı olarak silinecektir.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteDocumentCancel} color="primary">
            İptal
          </Button>
          <Button onClick={handleDeleteDocumentConfirm} color="error" variant="contained">
            Sil
          </Button>
        </DialogActions>
      </Dialog>

      {/* Başarı Mesajı Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
      >
        <Alert onClose={handleSnackbarClose} severity="success" sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

const ProtectedHome = () => (
  <WithAuth>
    <Home />
  </WithAuth>
);

export default ProtectedHome;
