import React, { useState } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Container,
  Link,
} from '@mui/material';
import apiClient from '../api/client';

const Register: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
      setLoading(true);
    setError('');

    try {
      await apiClient.post('/auth/register', {
        username,
        password,
        email: email || undefined,
      });
      setSuccess(true);
      setTimeout(() => {
        router.push('/login');
      }, 2000);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Kayıt olurken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Container component="main" maxWidth="xs">
      <Box
        sx={{
            marginTop: 8,
          display: 'flex',
            flexDirection: 'column',
          alignItems: 'center',
        }}
      >
          <Paper elevation={3} sx={{ padding: 4, width: '100%' }}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
          }}
        >
              <Typography component="h1" variant="h4" gutterBottom>
                Kayıt Başarılı!
              </Typography>
              <Alert severity="success" sx={{ width: '100%', mb: 2 }}>
                Hesabınız başarıyla oluşturuldu. Giriş sayfasına yönlendiriliyorsunuz...
          </Alert>
            </Box>
        </Paper>
      </Box>
      </Container>
    );
  }

  return (
    <Container component="main" maxWidth="xs">
    <Box
      sx={{
          marginTop: 8,
        display: 'flex',
          flexDirection: 'column',
        alignItems: 'center',
      }}
    >
        <Paper elevation={3} sx={{ padding: 4, width: '100%' }}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
        }}
      >
            <Typography component="h1" variant="h4" gutterBottom>
          Kayıt Ol
        </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ mb: 3 }}>
              Dijital İkiz Sistemi'ne yeni hesap oluşturun
        </Typography>

        {error && (
              <Alert severity="error" sx={{ width: '100%', mb: 2 }}>
            {error}
          </Alert>
        )}

            <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1, width: '100%' }}>
          <TextField
            margin="normal"
            required
            fullWidth
            id="username"
            label="Kullanıcı Adı"
                name="username"
            autoComplete="username"
            autoFocus
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                disabled={loading}
                helperText="En az 3 karakter olmalıdır"
          />
          <TextField
            margin="normal"
            fullWidth
            id="email"
                label="E-posta (İsteğe bağlı)"
                name="email"
            type="email"
            autoComplete="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={loading}
          />
          <TextField
            margin="normal"
            required
            fullWidth
                name="password"
            label="Şifre"
            type="password"
            id="password"
            autoComplete="new-password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={loading}
                helperText="En az 6 karakter olmalıdır"
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2 }}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Kayıt Ol'}
          </Button>

              <Box textAlign="center">
              <Link
                component="button"
                variant="body2"
                  onClick={() => router.push('/login')}
                  disabled={loading}
                  sx={{ textDecoration: 'none' }}
              >
                  Zaten hesabınız var mı? Giriş Yapın
              </Link>
              </Box>
            </Box>

            <Box sx={{ mt: 3, p: 2, backgroundColor: 'grey.100', borderRadius: 1, width: '100%' }}>
              <Typography variant="subtitle2" gutterBottom>
                Bilgi:
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Normal kullanıcılar sadece var olan dijital ikizlerle sohbet edebilir.
            </Typography>
          </Box>
        </Box>
      </Paper>
    </Box>
    </Container>
  );
};

export default Register;
