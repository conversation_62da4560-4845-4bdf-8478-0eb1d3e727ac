import React, { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  CircularProgress,
  Alert,
  Avatar,
  IconButton,
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import apiClient from '../../api/client';
import MarkdownRenderer from '../../components/MarkdownRenderer';
import { WithAuth, useAuth } from '../../contexts/AuthContext';

interface Message {
  id: string | number;
  chat_id: number;
  is_user: boolean;
  content: string;
  created_at: string;
}

interface DigitalTwin {
  id: number;
  name: string;
}

const Chat: React.FC = () => {
  const router = useRouter();
  const { id } = router.query;
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [sending, setSending] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [digitalTwin, setDigitalTwin] = useState<DigitalTwin | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (id) {
      fetchMessages();
    }
  }, [id, router]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchMessages = async () => {
    try {
      setLoading(true);

      // Fetch messages
      const messagesResponse = await apiClient.get(`/chats/${id}/messages`);
      setMessages(messagesResponse.data);

      // Fetch chat details to get digital twin info
      if (messagesResponse.data.length > 0) {
        const chatId = messagesResponse.data[0].chat_id;
        const chatResponse = await apiClient.get(`/chats/${chatId}`);

        // Fetch digital twin details
        const digitalTwinResponse = await apiClient.get(`/digital-twins/${chatResponse.data.digital_twin_id}`);
        setDigitalTwin(digitalTwinResponse.data);
      }

      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Mesajlar yüklenirken bir hata oluştu.');
    } finally {
      setLoading(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim()) return;

    try {
      setSending(true);

      const response = await apiClient.post(`/chats/${id}/messages`, {
        content: newMessage,
      });

      // Add both user message and bot response to chat
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          id: response.data.user_message_id,
          chat_id: Number(id),
          is_user: true,
          content: newMessage,
          created_at: new Date().toISOString(),
        },
        {
          id: response.data.bot_message_id,
          chat_id: Number(id),
          is_user: false,
          content: response.data.bot_response,
          created_at: new Date().toISOString(),
        },
      ]);

      setNewMessage('');
      setError(null);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Mesaj gönderilirken bir hata oluştu.');
    } finally {
      setSending(false);
    }
  };

  const handleBackToList = () => {
    router.push('/');
  };

  const getInitials = (name: string) => {
    if (!name) return '?';
    const names = name.split(' ');
    if (names.length > 1) {
      return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase();
    }
    return name[0].toUpperCase();
  };


  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ height: 'calc(100vh - 150px)', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <IconButton onClick={handleBackToList} sx={{ mr: 1 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h5" component="h1">
          {digitalTwin ? `Sohbet: ${digitalTwin.name}` : 'Sohbet'}
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper
        elevation={3}
        sx={{
          flex: 1,
          mb: 2,
          p: 2,
          overflow: 'auto',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {messages.length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <Typography variant="body1" color="text.secondary">
              Henüz mesaj yok. Sohbete başlamak için bir mesaj gönderin.
            </Typography>
          </Box>
        ) : (
          messages.map((message) => (
            <Box
              key={message.id}
              sx={{
                display: 'flex',
                justifyContent: message.is_user ? 'flex-end' : 'flex-start',
                mb: 2,
              }}
            >
              {!message.is_user && (
                <Avatar
                  sx={{ mr: 1, bgcolor: 'primary.main' }}
                >
                  {getInitials(digitalTwin?.name || '')}
                </Avatar>
              )}

              <Paper
                elevation={1}
                sx={{
                  p: 2,
                  maxWidth: '70%',
                  backgroundColor: message.is_user ? 'primary.light' : 'background.paper',
                  color: message.is_user ? 'white' : 'text.primary',
                  borderRadius: 2,
                }}
              >
                {message.is_user ? (
                  <Typography variant="body1">
                    {message.content}
                  </Typography>
                ) : (
                  <MarkdownRenderer content={message.content} />
                )}
                <Typography variant="caption" color={message.is_user ? 'white' : 'text.secondary'} sx={{ display: 'block', mt: 1 }}>
                  {new Date(message.created_at).toLocaleTimeString()}
                </Typography>
              </Paper>

              {message.is_user && (
                <Avatar
                  sx={{ ml: 1, bgcolor: 'secondary.main' }}
                >
                  {getInitials(user?.username || '')}
                </Avatar>
              )}
            </Box>
          ))
        )}
        <div ref={messagesEndRef} />
      </Paper>

      <Paper elevation={3} sx={{ p: 2 }}>
        <Box
          component="form"
          onSubmit={handleSendMessage}
          sx={{ display: 'flex', alignItems: 'center' }}
        >
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder="Mesajınızı yazın..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage(e);
              }
            }}
            disabled={sending}
            sx={{ mr: 1 }}
          />
          <Button
            type="submit"
            variant="contained"
            endIcon={sending ? <CircularProgress size={20} /> : <SendIcon />}
            disabled={sending || !newMessage.trim()}
          >
            Gönder
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

const ProtectedChat = () => (
  <WithAuth>
    <Chat />
  </WithAuth>
);

export default ProtectedChat;
