import React, { useState } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Container,
  Link,
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';

const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { login } = useAuth();
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
      setLoading(true);
    setError('');

    try {
      await login(username, password);
    } catch (err: any) {
      setError(err.response?.data?.detail || '<PERSON>iri<PERSON> yaparken bir hata o<PERSON>');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container component="main" maxWidth="xs">
    <Box
      sx={{
          marginTop: 8,
        display: 'flex',
          flexDirection: 'column',
        alignItems: 'center',
      }}
    >
        <Paper elevation={3} sx={{ padding: 4, width: '100%' }}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
        }}
      >
            <Typography component="h1" variant="h4" gutterBottom>
          Giriş Yap
        </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ mb: 3 }}>
              Dijital İkiz Sistemi'ne giriş yapın
        </Typography>

        {error && (
              <Alert severity="error" sx={{ width: '100%', mb: 2 }}>
            {error}
          </Alert>
        )}

            <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1, width: '100%' }}>
          <TextField
            margin="normal"
            required
            fullWidth
            id="username"
            label="Kullanıcı Adı"
                name="username"
            autoComplete="username"
            autoFocus
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                disabled={loading}
          />
          <TextField
            margin="normal"
            required
            fullWidth
                name="password"
            label="Şifre"
            type="password"
            id="password"
            autoComplete="current-password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={loading}
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2 }}
            disabled={loading}
          >
            {loading ? <CircularProgress size={24} /> : 'Giriş Yap'}
          </Button>

              <Box textAlign="center">
              <Link
                component="button"
                variant="body2"
                  onClick={() => router.push('/register')}
                  disabled={loading}
                  sx={{ textDecoration: 'none' }}
              >
                  Hesabınız yok mu? Kayıt Olun
              </Link>
              </Box>
          </Box>
        </Box>
      </Paper>
    </Box>
    </Container>
  );
};

export default Login;
