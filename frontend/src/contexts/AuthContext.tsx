import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/router';
import apiClient from '../api/client';

interface User {
  id: number;
  username: string;
  email?: string;
  is_active: boolean;
  created_at: string;
  role?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const router = useRouter();

  // Check if user is authenticated on app start
  useEffect(() => {
    checkAuth();
  }, []);

  // Set up axios interceptor for token
  useEffect(() => {
    const token = localStorage.getItem('access_token');
    if (token) {
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    }

    // Add response interceptor to handle 401 errors
    const interceptor = apiClient.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token süresinin dolup dolmadığını kontrol et
          const now = Math.floor(Date.now() / 1000);
          const token = localStorage.getItem('access_token');
          
          if (token) {
            try {
              // Token decode et ve süresi kontrol et
              const payload = JSON.parse(atob(token.split('.')[1]));
              if (payload.exp && payload.exp < now) {
                console.log('Token süresi dolmuş, otomatik çıkış yapılıyor...');
              }
            } catch (e) {
              console.log('Token geçersiz, çıkış yapılıyor...');
            }
          }
          
          logout();
        }
        return Promise.reject(error);
      }
    );

    return () => {
      apiClient.interceptors.response.eject(interceptor);
    };
  }, []);

  const getToken = () => {
    // Önce localStorage, sonra sessionStorage kontrol et
    return localStorage.getItem('access_token') || sessionStorage.getItem('access_token');
  };

  const checkAuth = async () => {
    try {
      const token = getToken();
      if (!token) {
        setLoading(false);
        return;
      }

      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      const response = await apiClient.get('/auth/me');
      setUser(response.data);
    } catch (error) {
      console.error('Auth check failed:', error);
      // Token is invalid, remove it from both storages
      localStorage.removeItem('access_token');
      sessionStorage.removeItem('access_token');
      localStorage.removeItem('remember_me');
      delete apiClient.defaults.headers.common['Authorization'];
    } finally {
      setLoading(false);
    }
  };

  const login = async (username: string, password: string, rememberMe: boolean = true) => {
    try {
      const response = await apiClient.post('/auth/login', { username, password });
      const { access_token } = response.data;

      // Remember me seçeneğine göre storage yöntemi belirle
      if (rememberMe) {
        localStorage.setItem('access_token', access_token);
        localStorage.setItem('remember_me', 'true');
      } else {
        sessionStorage.setItem('access_token', access_token);
        localStorage.removeItem('remember_me');
      }
      
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${access_token}`;

      // Get user info
      const userResponse = await apiClient.get('/auth/me');
      setUser(userResponse.data);

      // Use window.location for hard redirect to ensure state is properly updated
      window.location.href = '/';
    } catch (error) {
      console.error('AuthContext: Login error', error);
      throw error;
    }
  };

  const logout = () => {
    // Tüm storage'lardan token'ları temizle
    localStorage.removeItem('access_token');
    sessionStorage.removeItem('access_token');
    localStorage.removeItem('remember_me');
    delete apiClient.defaults.headers.common['Authorization'];
    setUser(null);
    router.push('/login');
  };

  // Admin check
  const isAdmin = React.useMemo(() => {
    if (!user) return false;
    
    const adminCheck = user.username === 'admin' || user.role === 'admin';
    
    return adminCheck;
  }, [user]);

  const value: AuthContextType = {
    user,
    loading,
    login,
    logout,
    isAuthenticated: !!user,
    isAdmin,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Higher-order component for protected routes
interface WithAuthProps {
  children: ReactNode;
}

export const WithAuth: React.FC<WithAuthProps> = ({ children }) => {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        Loading...
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return <>{children}</>;
};

// Higher-order component for admin-only routes
interface WithAdminProps {
  children: ReactNode;
}

export const WithAdmin: React.FC<WithAdminProps> = ({ children }) => {
  const { user, loading, isAdmin } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        router.push('/login');
      } else if (!isAdmin) {
        router.push('/');
      }
    }
  }, [user, loading, isAdmin, router]);

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        Loading...
      </div>
    );
  }

  if (!user || !isAdmin) {
    return null;
  }

  return <>{children}</>;
};
