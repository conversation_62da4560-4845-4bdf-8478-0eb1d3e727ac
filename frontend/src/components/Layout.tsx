import React, { ReactNode } from 'react';
import { App<PERSON><PERSON>, Too<PERSON><PERSON>, Typo<PERSON>, Button, Container, Box, Menu, MenuItem, IconButton } from '@mui/material';
import { useRouter } from 'next/router';
import { useAuth } from '../contexts/AuthContext';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const router = useRouter();
  const { user, logout, isAuthenticated, isAdmin } = useAuth();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  // Layout effect
  React.useEffect(() => {
    // Component mounted
  }, [user, isAdmin]);

  const handleNavigation = (path: string) => {
    router.push(path);
  };

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleClose();
  };

  // Don't show layout for login and register pages
  if (router.pathname === '/login' || router.pathname === '/register') {
    return <>{children}</>;
  }

  return (
    <>
      <AppBar position="static">
        <Toolbar>
          <Typography
            variant="h6"
            component="div"
            sx={{ flexGrow: 1, cursor: 'pointer' }}
            onClick={() => handleNavigation('/')}
          >
            Dijital İkiz Sistemi
          </Typography>

          {isAuthenticated ? (
            <>
              {/* Ana navigasyon */}
              <Button color="inherit" onClick={() => handleNavigation('/')}>
                Ana Sayfa
              </Button>

              {/* Admin yetkilerini gerektiren butonlar */}
              {isAdmin && (
                <>
                  <Button color="inherit" onClick={() => handleNavigation('/create-digital-twin')}>
                    Yeni Dijital İkiz
                  </Button>
                  <Button color="inherit" onClick={() => handleNavigation('/upload-document')}>
                    Doküman Yükle
                  </Button>
                </>
              )}

              {/* Kullanıcı menüsü */}
              <IconButton
                size="large"
                aria-label="account of current user"
                aria-controls="menu-appbar"
                aria-haspopup="true"
                onClick={handleMenu}
                color="inherit"
              >
                <AccountCircleIcon />
              </IconButton>
              <Menu
                id="menu-appbar"
                anchorEl={anchorEl}
                anchorOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                keepMounted
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                open={Boolean(anchorEl)}
                onClose={handleClose}
              >
                <MenuItem onClick={handleClose}>
                  <Typography variant="body2" color="text.secondary">
                    {user?.username} {isAdmin ? '(Admin)' : '(Kullanıcı)'}
                  </Typography>
                </MenuItem>
                <MenuItem onClick={handleLogout}>Çıkış Yap</MenuItem>
              </Menu>
            </>
          ) : (
            <>
              <Button color="inherit" onClick={() => handleNavigation('/login')}>
                Giriş Yap
              </Button>
              <Button color="inherit" onClick={() => handleNavigation('/register')}>
                Kayıt Ol
              </Button>
            </>
          )}
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box sx={{ py: 2 }}>
          {children}
        </Box>
      </Container>
    </>
  );
};

export default Layout;
