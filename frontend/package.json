{"name": "digital-twin-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.16", "@mui/material": "^5.14.16", "axios": "^1.6.0", "next": "13.5.6", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.0.1", "typescript": "^5"}}