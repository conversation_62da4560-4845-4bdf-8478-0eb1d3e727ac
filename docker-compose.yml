services:
  postgres:
    image: postgres:14
    container_name: digital-twin-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: digital_twin_db
      LC_COLLATE: tr_TR.UTF-8
      LC_CTYPE: tr_TR.UTF-8
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=tr_TR.UTF-8 --auth-local=trust --auth-host=scram-sha-256"
      POSTGRES_MAX_CONNECTIONS: 200
    ports:
      - "5433:5432"
    volumes:
      - ./docker_data/postgres:/var/lib/postgresql/data
      - ./postgres-init:/docker-entrypoint-initdb.d
    mem_limit: 1g
    cpus: 1.0
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    command: ["postgres", "-c", "shared_buffers=256MB", "-c", "max_connections=200", "-c", "log_statement=all", "-c", "logging_collector=off", "-c", "idle_in_transaction_session_timeout=300000", "-c", "statement_timeout=300000"]
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  qdrant:
    image: qdrant/qdrant:latest
    container_name: digital-twin-qdrant
    ports:
      - "6335:6333"
      - "6336:6334"
    volumes:
      - ./docker_data/qdrant:/qdrant/storage
    restart: unless-stopped

  ollama:
    image: ollama/ollama:latest
    container_name: digital-twin-ollama
    ports:
      - "11434:11434"
    volumes:
      - ./docker_data/ollama:/root/.ollama
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: digital-twin-backend
    ports:
      - "8001:8000"
    volumes:
      - ./backend:/app
      - ./docker_data/ocr_models:/root/.EasyOCR
    environment:
      - DATABASE_URL=********************************************/digital_twin_db
      - PYTHONUNBUFFERED=1
    mem_limit: 2g
    cpus: 2.0
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=200m
    depends_on:
      postgres:
        condition: service_healthy
      ollama:
        condition: service_started
      qdrant:
        condition: service_started
    restart: unless-stopped
    command: |
      sh -c "
        python init_ocr_models.py &&
        uvicorn main:app --host 0.0.0.0 --port 8000 --workers 1 --timeout-keep-alive 300
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 240s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: digital-twin-frontend
    ports:
      - "3001:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
