#!/bin/bash

echo "🔒 Digital Twin Güvenlik Kontrolü Başlatılıyor..."

# Bilinen miner process'lerini kontrol et
echo "🔍 Miner process'leri kontrol ediliyor..."
MINERS=$(ps aux | grep -E "(kinsing|kdevtmpfsi|mining|crypto|xmrig|monero)" | grep -v grep)
if [ ! -z "$MINERS" ]; then
    echo "⚠️  Şüpheli process'ler bulundu:"
    echo "$MINERS"
    echo "🧹 Temizleniyor..."
    sudo pkill -f kinsing
    sudo pkill -f kdevtmpfsi
    sudo pkill -f xmrig
    sudo pkill -f monero
    sudo rm -f /tmp/kinsing /tmp/kdevtmpfsi /tmp/xmrig
else
    echo "✅ Miner process'i bulunamadı"
fi

# Şüpheli dosyaları kontrol et
echo "🔍 Şüpheli dosyalar kontrol ediliyor..."
SUSPICIOUS_FILES=$(find /tmp -name "*kinsing*" -o -name "*kdevtmpfsi*" -o -name "*xmrig*" 2>/dev/null)
if [ ! -z "$SUSPICIOUS_FILES" ]; then
    echo "⚠️  Şüpheli dosyalar bulundu:"
    echo "$SUSPICIOUS_FILES"
    echo "🧹 Temizleniyor..."
    sudo rm -f $SUSPICIOUS_FILES
else
    echo "✅ Şüpheli dosya bulunamadı"
fi

# Network bağlantılarını kontrol et
echo "🔍 Şüpheli network bağlantıları kontrol ediliyor..."
SUSPICIOUS_CONNECTIONS=$(netstat -tulpn 2>/dev/null | grep -E ":4444|:8080|:3333|:14444" | grep -v docker)
if [ ! -z "$SUSPICIOUS_CONNECTIONS" ]; then
    echo "⚠️  Şüpheli network bağlantıları:"
    echo "$SUSPICIOUS_CONNECTIONS"
else
    echo "✅ Şüpheli network bağlantısı bulunamadı"
fi

# Cron job'ları kontrol et
echo "🔍 Cron job'ları kontrol ediliyor..."
SUSPICIOUS_CRONS=$(crontab -l 2>/dev/null | grep -E "(kinsing|kdevtmpfsi|curl|wget.*tmp)")
if [ ! -z "$SUSPICIOUS_CRONS" ]; then
    echo "⚠️  Şüpheli cron job'lar:"
    echo "$SUSPICIOUS_CRONS"
    echo "🧹 Cron temizlenmesi gerekiyor (manuel kontrol edin)"
else
    echo "✅ Şüpheli cron job bulunamadı"
fi

# Docker container'ları kontrol et
echo "🔍 Docker container'ları kontrol ediliyor..."
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Sistem kaynaklarını kontrol et
echo "🔍 Sistem kaynakları:"
free -h
echo ""
docker stats --no-stream

echo "✅ Güvenlik kontrolü tamamlandı!"
