name: Deploy digital twin main branch

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy via SSH and tmux
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.SERVER_IP }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          script: |
            cd /root/digital_twin/digital_twin

            git fetch origin main
            git checkout main
            git pull origin main

            tmux kill-session -t dt || true
            tmux new-session -d -s dt -n docker

            # Window 0: Restart Docker containers
            tmux send-keys -t dt:docker 'cd /root/digital_twin/digital_twin && docker compose down && docker compose up --build' C-m

            echo "Deployment completed in tmux session."